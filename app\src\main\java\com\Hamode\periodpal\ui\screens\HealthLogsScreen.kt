package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.components.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HealthLogsScreen(
    onNavigateToSymptomLogging: (LocalDate) -> Unit,
    onNavigateToHistory: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedFilter by remember { mutableStateOf(LogFilter.ALL) }
    val sampleLogs = getSampleDailyLogs()
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        SoftPink80.copy(alpha = 0.1f)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Health Logs",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold
                )
            },
            actions = {
                IconButton(onClick = onNavigateToHistory) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = "View history"
                    )
                }
                
                IconButton(onClick = { onNavigateToSymptomLogging(LocalDate.now()) }) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add new log"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )
        
        // Filter Tabs
        LogFilterTabs(
            selectedFilter = selectedFilter,
            onFilterSelected = { selectedFilter = it },
            modifier = Modifier.fillMaxWidth()
        )
        
        // Content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(vertical = 16.dp, bottom = 88.dp)
        ) {
            // Quick Stats
            item {
                QuickLogsStats(
                    totalLogs = sampleLogs.size,
                    recentSymptoms = sampleLogs.take(5).flatMap { it.symptoms }.distinct().size,
                    averagePainLevel = sampleLogs.map { it.painLevel.level }.average(),
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // Recent Logs
            item {
                Text(
                    text = "Recent Logs",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            // Filter logs based on selected filter
            val filteredLogs = when (selectedFilter) {
                LogFilter.ALL -> sampleLogs
                LogFilter.PERIOD_DAYS -> sampleLogs.filter { it.isPeriodDay() }
                LogFilter.SYMPTOMS -> sampleLogs.filter { it.symptoms.isNotEmpty() }
                LogFilter.MOOD -> sampleLogs.filter { it.moods.isNotEmpty() }
                LogFilter.PAIN -> sampleLogs.filter { it.painLevel != PainLevel.NONE }
            }
            
            items(filteredLogs) { dailyLog ->
                DailyLogCard(
                    dailyLog = dailyLog,
                    onEdit = { onNavigateToSymptomLogging(dailyLog.date) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            if (filteredLogs.isEmpty()) {
                item {
                    EmptyLogsState(
                        filter = selectedFilter,
                        onAddLog = { onNavigateToSymptomLogging(LocalDate.now()) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

enum class LogFilter(val displayName: String) {
    ALL("All"),
    PERIOD_DAYS("Period Days"),
    SYMPTOMS("Symptoms"),
    MOOD("Mood"),
    PAIN("Pain")
}

@Composable
private fun LogFilterTabs(
    selectedFilter: LogFilter,
    onFilterSelected: (LogFilter) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = LightGray.copy(alpha = 0.3f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier.padding(4.dp)
        ) {
            LogFilter.values().forEach { filter ->
                val isSelected = selectedFilter == filter
                
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            color = if (isSelected) MaterialTheme.colorScheme.surface else androidx.compose.ui.graphics.Color.Transparent,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(vertical = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = filter.displayName,
                        style = MaterialTheme.typography.labelMedium,
                        color = if (isSelected) RosePink40 else DarkGray.copy(alpha = 0.7f),
                        fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                    )
                }
            }
        }
    }
}

@Composable
private fun QuickLogsStats(
    totalLogs: Int,
    recentSymptoms: Int,
    averagePainLevel: Double,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick Stats",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                QuickStatItem(
                    title = "Total Logs",
                    value = totalLogs.toString(),
                    icon = Icons.Default.Assignment,
                    color = RosePink40
                )
                
                QuickStatItem(
                    title = "Recent Symptoms",
                    value = recentSymptoms.toString(),
                    icon = Icons.Default.MedicalServices,
                    color = WarningOrange
                )
                
                QuickStatItem(
                    title = "Avg Pain",
                    value = String.format("%.1f/4", averagePainLevel),
                    icon = Icons.Default.LocalHospital,
                    color = if (averagePainLevel > 2) ErrorRed else SuccessGreen
                )
            }
        }
    }
}

@Composable
private fun QuickStatItem(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: androidx.compose.ui.graphics.Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = title,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun DailyLogCard(
    dailyLog: DailyLog,
    onEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = dailyLog.date.format(DateTimeFormatter.ofPattern("MMM dd, yyyy")),
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    Text(
                        text = dailyLog.date.format(DateTimeFormatter.ofPattern("EEEE")),
                        style = MaterialTheme.typography.bodySmall,
                        color = DarkGray.copy(alpha = 0.7f)
                    )
                }
                
                IconButton(onClick = onEdit) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit log",
                        tint = RosePink40
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Log summary
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                if (dailyLog.flowIntensity != FlowIntensity.NONE) {
                    LogSummaryItem(
                        label = "Flow",
                        value = dailyLog.flowIntensity.displayName,
                        color = PeriodPalThemeColors.getFlowColor(dailyLog.flowIntensity)
                    )
                }
                
                if (dailyLog.painLevel != PainLevel.NONE) {
                    LogSummaryItem(
                        label = "Pain",
                        value = dailyLog.painLevel.level.toString(),
                        color = PeriodPalThemeColors.getPainColor(dailyLog.painLevel)
                    )
                }
                
                if (dailyLog.moods.isNotEmpty()) {
                    LogSummaryItem(
                        label = "Mood",
                        value = dailyLog.moods.first().emoji,
                        color = PeriodPalThemeColors.getMoodColor(dailyLog.moods.first())
                    )
                }
            }
            
            if (dailyLog.symptoms.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Symptoms: ${dailyLog.symptoms.joinToString(", ") { it.displayName }}",
                    style = MaterialTheme.typography.bodySmall,
                    color = DarkGray.copy(alpha = 0.8f)
                )
            }
        }
    }
}

@Composable
private fun LogSummaryItem(
    label: String,
    value: String,
    color: androidx.compose.ui.graphics.Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.labelMedium,
            color = color,
            fontWeight = FontWeight.SemiBold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun EmptyLogsState(
    filter: LogFilter,
    onAddLog: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Assignment,
                contentDescription = "No logs",
                tint = DarkGray.copy(alpha = 0.3f),
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "No ${filter.displayName.lowercase()} logs found",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Start tracking your health data to see insights here",
                style = MaterialTheme.typography.bodyMedium,
                color = DarkGray.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onAddLog,
                colors = ButtonDefaults.buttonColors(
                    containerColor = RosePink40
                )
            ) {
                Text("Add First Log")
            }
        }
    }
}

@Composable
private fun getSampleDailyLogs(): List<DailyLog> {
    return listOf(
        DailyLog(
            date = LocalDate.now(),
            flowIntensity = FlowIntensity.MEDIUM,
            moods = listOf(MoodType.HAPPY),
            painLevel = PainLevel.MILD,
            symptoms = listOf(SymptomType.CRAMPS, SymptomType.BLOATING)
        ),
        DailyLog(
            date = LocalDate.now().minusDays(1),
            flowIntensity = FlowIntensity.LIGHT,
            moods = listOf(MoodType.TIRED),
            painLevel = PainLevel.MODERATE,
            symptoms = listOf(SymptomType.HEADACHE)
        ),
        DailyLog(
            date = LocalDate.now().minusDays(2),
            flowIntensity = FlowIntensity.HEAVY,
            moods = listOf(MoodType.IRRITABLE),
            painLevel = PainLevel.SEVERE,
            symptoms = listOf(SymptomType.CRAMPS, SymptomType.BACK_PAIN, SymptomType.FATIGUE)
        )
    )
}

@Preview(showBackground = true, name = "Health Logs Screen Preview")
@Composable
fun HealthLogsScreenPreview() {
    PeriodPalTheme {
        HealthLogsScreen(
            onNavigateToSymptomLogging = { },
            onNavigateToHistory = { }
        )
    }
}
