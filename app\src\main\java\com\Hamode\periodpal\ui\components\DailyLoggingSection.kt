package com.Hamode.periodpal.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun DailyLoggingSection(
    selectedDate: LocalDate,
    flowIntensity: FlowIntensity,
    onFlowIntensityChanged: (FlowIntensity) -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedMoods by remember { mutableStateOf(setOf<MoodType>()) }
    var selectedPainLevel by remember { mutableStateOf(PainLevel.NONE) }
    var selectedSymptoms by remember { mutableStateOf(setOf<SymptomType>()) }
    
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Daily Log",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = selectedDate.format(DateTimeFormatter.ofPattern("MMM dd")),
                    style = MaterialTheme.typography.bodyMedium,
                    color = RosePink40
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Quick Symptom Logger
            QuickSymptomLogger(
                selectedDate = selectedDate,
                currentLog = DailyLog(
                    date = selectedDate,
                    flowIntensity = flowIntensity,
                    moods = selectedMoods.toList(),
                    painLevel = selectedPainLevel,
                    symptoms = selectedSymptoms.toList()
                ),
                onQuickLog = { dailyLog ->
                    onFlowIntensityChanged(dailyLog.flowIntensity)
                    selectedMoods = dailyLog.moods.toSet()
                    selectedPainLevel = dailyLog.painLevel
                    selectedSymptoms = dailyLog.symptoms.toSet()
                },
                onOpenFullLogger = { /* Navigate to full symptom logging screen */ },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun FlowIntensitySelector(
    selectedIntensity: FlowIntensity,
    onIntensitySelected: (FlowIntensity) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "Flow Intensity",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(FlowIntensity.values()) { intensity ->
                FlowIntensityChip(
                    intensity = intensity,
                    isSelected = selectedIntensity == intensity,
                    onClick = { onIntensitySelected(intensity) }
                )
            }
        }
    }
}

@Composable
private fun FlowIntensityChip(
    intensity: FlowIntensity,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) {
        PeriodPalThemeColors.getFlowColor(intensity)
    } else {
        LightGray
    }
    
    val textColor = if (isSelected) {
        if (intensity == FlowIntensity.NONE || intensity == FlowIntensity.SPOTTING) {
            DarkGray
        } else {
            White
        }
    } else {
        DarkGray
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = intensity.displayName,
            style = MaterialTheme.typography.labelMedium,
            color = textColor,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

@Composable
private fun MoodSelector(
    selectedMoods: Set<MoodType>,
    onMoodsChanged: (Set<MoodType>) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "How are you feeling?",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(MoodType.values().take(6)) { mood ->
                MoodChip(
                    mood = mood,
                    isSelected = selectedMoods.contains(mood),
                    onClick = {
                        val newMoods = if (selectedMoods.contains(mood)) {
                            selectedMoods - mood
                        } else {
                            selectedMoods + mood
                        }
                        onMoodsChanged(newMoods)
                    }
                )
            }
        }
    }
}

@Composable
private fun MoodChip(
    mood: MoodType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) {
        RosePink40.copy(alpha = 0.2f)
    } else {
        LightGray
    }
    
    val borderColor = if (isSelected) {
        RosePink40
    } else {
        Color.Transparent
    }
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(8.dp)
            .width(60.dp)
    ) {
        Text(
            text = mood.emoji,
            fontSize = 24.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        Text(
            text = mood.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

@Composable
private fun PainLevelSelector(
    selectedPainLevel: PainLevel,
    onPainLevelChanged: (PainLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "Pain Level",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            PainLevel.values().forEach { painLevel ->
                PainLevelIndicator(
                    painLevel = painLevel,
                    isSelected = selectedPainLevel == painLevel,
                    onClick = { onPainLevelChanged(painLevel) }
                )
            }
        }
    }
}

@Composable
private fun PainLevelIndicator(
    painLevel: PainLevel,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(if (isSelected) 32.dp else 24.dp)
                .clip(CircleShape)
                .background(PeriodPalThemeColors.getPainColor(painLevel))
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = painLevel.level.toString(),
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) RosePink40 else DarkGray,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun QuickSymptomsSelector(
    selectedSymptoms: Set<SymptomType>,
    onSymptomsChanged: (Set<SymptomType>) -> Unit,
    modifier: Modifier = Modifier
) {
    val commonSymptoms = listOf(
        SymptomType.CRAMPS,
        SymptomType.BLOATING,
        SymptomType.HEADACHE,
        SymptomType.BREAST_TENDERNESS,
        SymptomType.FATIGUE
    )
    
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Common Symptoms",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            TextButton(
                onClick = { /* Navigate to full symptoms list */ }
            ) {
                Text(
                    text = "View All",
                    color = RosePink40
                )
            }
        }
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(commonSymptoms) { symptom ->
                SymptomChip(
                    symptom = symptom,
                    isSelected = selectedSymptoms.contains(symptom),
                    onClick = {
                        val newSymptoms = if (selectedSymptoms.contains(symptom)) {
                            selectedSymptoms - symptom
                        } else {
                            selectedSymptoms + symptom
                        }
                        onSymptomsChanged(newSymptoms)
                    }
                )
            }
        }
    }
}

@Composable
private fun SymptomChip(
    symptom: SymptomType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) {
        RosePink40.copy(alpha = 0.2f)
    } else {
        LightGray
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Text(
            text = symptom.icon,
            fontSize = 16.sp,
            modifier = Modifier.padding(end = 4.dp)
        )
        Text(
            text = symptom.displayName,
            style = MaterialTheme.typography.labelMedium,
            color = if (isSelected) RosePink40 else DarkGray
        )
    }
}

@Preview(showBackground = true, name = "Daily Logging Section Preview")
@Composable
fun DailyLoggingSectionPreview() {
    PeriodPalTheme {
        DailyLoggingSection(
            selectedDate = LocalDate.now(),
            flowIntensity = FlowIntensity.MEDIUM,
            onFlowIntensityChanged = { }
        )
    }
}
