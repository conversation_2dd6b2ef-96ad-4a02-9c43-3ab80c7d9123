package com.Hamode.periodpal.data.models

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Represents a daily log entry for tracking period-related data
 */
data class DailyLog(
    val id: String = "",
    val date: LocalDate,
    val flowIntensity: FlowIntensity = FlowIntensity.NONE,
    val moods: List<MoodType> = emptyList(),
    val painLevel: PainLevel = PainLevel.NONE,
    val symptoms: List<SymptomType> = emptyList(),
    val notes: String = "",
    val weight: Double? = null, // in kg
    val temperature: Double? = null, // in Celsius
    val sleepHours: Double? = null,
    val waterIntake: Int? = null, // in ml
    val exerciseMinutes: Int? = null,
    val medicationTaken: List<String> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    /**
     * Checks if this is a period day (has any flow)
     */
    fun isPeriodDay(): Boolean = flowIntensity != FlowIntensity.NONE
    
    /**
     * Gets the overall mood score for the day
     */
    fun getMoodScore(): Int {
        if (moods.isEmpty()) return 0
        
        val positiveCount = moods.count { it in MoodType.getPositiveMoods() }
        val negativeCount = moods.count { it in MoodType.getNegativeMoods() }
        
        return when {
            positiveCount > negativeCount -> 1 // Positive day
            negativeCount > positiveCount -> -1 // Negative day
            else -> 0 // Neutral day
        }
    }
    
    /**
     * Checks if this day has PMS-like symptoms
     */
    fun hasPMSSymptoms(): Boolean {
        val pmsSymptoms = listOf(
            SymptomType.CRAMPS,
            SymptomType.BLOATING,
            SymptomType.BREAST_TENDERNESS,
            SymptomType.HEADACHE,
            SymptomType.FATIGUE
        )
        return symptoms.any { it in pmsSymptoms } || 
               moods.any { it in listOf(MoodType.IRRITABLE, MoodType.ANXIOUS, MoodType.EMOTIONAL) } ||
               painLevel.level >= 2
    }
}
