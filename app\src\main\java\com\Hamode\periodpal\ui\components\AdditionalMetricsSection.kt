package com.Hamode.periodpal.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.ui.theme.*

@Composable
fun AdditionalMetricsSection(
    weight: String,
    onWeightChanged: (String) -> Unit,
    temperature: String,
    onTemperatureChanged: (String) -> Unit,
    sleepHours: String,
    onSleepHoursChanged: (String) -> Unit,
    waterIntake: String,
    onWaterIntakeChanged: (String) -> Unit,
    exerciseMinutes: String,
    onExerciseMinutesChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Additional Metrics",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // First Row - Weight and Temperature
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                MetricInputField(
                    value = weight,
                    onValueChange = onWeightChanged,
                    label = "Weight",
                    unit = "kg",
                    icon = Icons.Default.MonitorWeight,
                    keyboardType = KeyboardType.Decimal,
                    modifier = Modifier.weight(1f)
                )
                
                MetricInputField(
                    value = temperature,
                    onValueChange = onTemperatureChanged,
                    label = "Temperature",
                    unit = "°C",
                    icon = Icons.Default.Thermostat,
                    keyboardType = KeyboardType.Decimal,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Second Row - Sleep and Water
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                MetricInputField(
                    value = sleepHours,
                    onValueChange = onSleepHoursChanged,
                    label = "Sleep",
                    unit = "hours",
                    icon = Icons.Default.Bedtime,
                    keyboardType = KeyboardType.Decimal,
                    modifier = Modifier.weight(1f)
                )
                
                MetricInputField(
                    value = waterIntake,
                    onValueChange = onWaterIntakeChanged,
                    label = "Water",
                    unit = "ml",
                    icon = Icons.Default.WaterDrop,
                    keyboardType = KeyboardType.Number,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Third Row - Exercise (full width)
            MetricInputField(
                value = exerciseMinutes,
                onValueChange = onExerciseMinutesChanged,
                label = "Exercise",
                unit = "minutes",
                icon = Icons.Default.FitnessCenter,
                keyboardType = KeyboardType.Number,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MetricInputField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    unit: String,
    icon: ImageVector,
    keyboardType: KeyboardType,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = RosePink40,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = label,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium
            )
        }
        
        OutlinedTextField(
            value = value,
            onValueChange = onValueChange,
            placeholder = {
                Text(
                    text = "Enter $label",
                    style = MaterialTheme.typography.bodyMedium,
                    color = DarkGray.copy(alpha = 0.5f)
                )
            },
            suffix = {
                Text(
                    text = unit,
                    style = MaterialTheme.typography.bodySmall,
                    color = DarkGray.copy(alpha = 0.7f)
                )
            },
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
            singleLine = true,
            shape = RoundedCornerShape(12.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = RosePink40,
                unfocusedBorderColor = LightGray,
                focusedLabelColor = RosePink40,
                cursorColor = RosePink40
            ),
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun NotesSection(
    notes: String,
    onNotesChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Notes,
                    contentDescription = "Notes",
                    tint = RosePink40,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Notes",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            OutlinedTextField(
                value = notes,
                onValueChange = onNotesChanged,
                placeholder = {
                    Text(
                        text = "Add any additional notes about your day...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = DarkGray.copy(alpha = 0.5f)
                    )
                },
                minLines = 3,
                maxLines = 5,
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = RosePink40,
                    unfocusedBorderColor = LightGray,
                    focusedLabelColor = RosePink40,
                    cursorColor = RosePink40
                ),
                modifier = Modifier.fillMaxWidth()
            )
            
            // Character count
            if (notes.isNotEmpty()) {
                Text(
                    text = "${notes.length} characters",
                    style = MaterialTheme.typography.labelSmall,
                    color = DarkGray.copy(alpha = 0.5f),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp)
                )
            }
        }
    }
}

@Preview(showBackground = true, name = "Additional Metrics Section Preview")
@Composable
fun AdditionalMetricsSectionPreview() {
    PeriodPalTheme {
        AdditionalMetricsSection(
            weight = "65.5",
            onWeightChanged = { },
            temperature = "36.8",
            onTemperatureChanged = { },
            sleepHours = "7.5",
            onSleepHoursChanged = { },
            waterIntake = "2000",
            onWaterIntakeChanged = { },
            exerciseMinutes = "30",
            onExerciseMinutesChanged = { }
        )
    }
}

@Preview(showBackground = true, name = "Notes Section Preview")
@Composable
fun NotesSectionPreview() {
    PeriodPalTheme {
        NotesSection(
            notes = "Feeling great today! Had a good workout and plenty of water.",
            onNotesChanged = { }
        )
    }
}
