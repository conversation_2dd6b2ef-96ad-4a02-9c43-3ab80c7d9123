package com.Hamode.periodpal.data.utils

import com.Hamode.periodpal.data.models.*
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.sqrt

/**
 * Utility class for cycle-related calculations
 */
object CycleCalculator {
    
    /**
     * Calculates cycle statistics from a list of completed cycles
     */
    fun calculateStatistics(cycles: List<MenstrualCycle>): CycleStatistics? {
        if (cycles.isEmpty()) return null
        
        val completedCycles = cycles.filter { it.isComplete }
        if (completedCycles.isEmpty()) return null
        
        val cycleLengths = completedCycles.mapNotNull { it.getActualCycleLength() }
        val periodLengths = completedCycles.map { it.getActualPeriodLength() }
        
        if (cycleLengths.isEmpty()) return null
        
        val avgCycleLength = cycleLengths.average()
        val avgPeriodLength = periodLengths.average()
        val cycleVariability = calculateStandardDeviation(cycleLengths.map { it.toDouble() })
        
        // Collect all symptoms and their frequencies
        val allSymptoms = mutableMapOf<SymptomType, Int>()
        val allMoods = mutableMapOf<MoodType, Int>()
        var totalPainLevels = 0.0
        var painCount = 0
        
        completedCycles.forEach { cycle ->
            cycle.dailyLogs.forEach { log ->
                log.symptoms.forEach { symptom ->
                    allSymptoms[symptom] = allSymptoms.getOrDefault(symptom, 0) + 1
                }
                log.moods.forEach { mood ->
                    allMoods[mood] = allMoods.getOrDefault(mood, 0) + 1
                }
                if (log.painLevel != PainLevel.NONE) {
                    totalPainLevels += log.painLevel.level
                    painCount++
                }
            }
        }
        
        val mostCommonSymptoms = allSymptoms.toList()
            .sortedByDescending { it.second }
            .take(10)
        
        return CycleStatistics(
            averageCycleLength = avgCycleLength,
            averagePeriodLength = avgPeriodLength,
            cycleVariability = cycleVariability,
            totalCycles = completedCycles.size,
            longestCycle = cycleLengths.maxOrNull() ?: 0,
            shortestCycle = cycleLengths.minOrNull() ?: 0,
            mostCommonSymptoms = mostCommonSymptoms,
            averagePainLevel = if (painCount > 0) totalPainLevels / painCount else 0.0,
            moodTrends = allMoods
        )
    }
    
    /**
     * Predicts next cycle based on historical data
     */
    fun predictNextCycle(
        cycles: List<MenstrualCycle>,
        userProfile: UserProfile
    ): CyclePrediction? {
        val completedCycles = cycles.filter { it.isComplete }
        if (completedCycles.isEmpty()) {
            // Use user profile defaults
            val lastPeriod = userProfile.lastPeriodStartDate ?: return null
            val nextStart = lastPeriod.plusDays(userProfile.averageCycleLength.toLong())
            return createBasicPrediction(nextStart, userProfile, 0.3)
        }
        
        val cycleLengths = completedCycles.mapNotNull { it.getActualCycleLength() }
        val periodLengths = completedCycles.map { it.getActualPeriodLength() }
        
        if (cycleLengths.isEmpty()) return null
        
        val avgCycleLength = cycleLengths.average().toInt()
        val avgPeriodLength = periodLengths.average().toInt()
        val variability = calculateStandardDeviation(cycleLengths.map { it.toDouble() })
        
        // Get the last cycle start date
        val lastCycleStart = completedCycles.maxByOrNull { it.startDate }?.startDate ?: return null
        val nextStart = lastCycleStart.plusDays(avgCycleLength.toLong())
        
        // Calculate confidence based on regularity and number of cycles
        val confidence = calculatePredictionConfidence(completedCycles.size, variability)
        
        return CyclePrediction(
            nextPeriodStart = nextStart,
            nextPeriodEnd = nextStart.plusDays((avgPeriodLength - 1).toLong()),
            nextFertileWindowStart = nextStart.plusDays((avgCycleLength / 2 - 2).toLong()),
            nextFertileWindowEnd = nextStart.plusDays((avgCycleLength / 2 + 2).toLong()),
            nextPMSStart = nextStart.plusDays((avgCycleLength - 7).toLong()),
            confidence = confidence,
            basedOnCycles = completedCycles.size
        )
    }
    
    /**
     * Determines if a date falls within the fertile window
     */
    fun isInFertileWindow(date: LocalDate, cycle: MenstrualCycle): Boolean {
        val fertileWindow = cycle.getFertileWindow() ?: return false
        return !date.isBefore(fertileWindow.first) && !date.isAfter(fertileWindow.second)
    }
    
    /**
     * Determines if a date falls within the PMS window
     */
    fun isInPMSWindow(date: LocalDate, cycle: MenstrualCycle): Boolean {
        val pmsWindow = cycle.getPMSWindow() ?: return false
        return !date.isBefore(pmsWindow.first) && !date.isAfter(pmsWindow.second)
    }
    
    /**
     * Calculates days until next period
     */
    fun daysUntilNextPeriod(prediction: CyclePrediction): Int {
        val today = LocalDate.now()
        return ChronoUnit.DAYS.between(today, prediction.nextPeriodStart).toInt()
    }
    
    private fun calculateStandardDeviation(values: List<Double>): Double {
        if (values.size < 2) return 0.0
        val mean = values.average()
        val variance = values.map { (it - mean) * (it - mean) }.average()
        return sqrt(variance)
    }
    
    private fun calculatePredictionConfidence(cycleCount: Int, variability: Double): Double {
        val cycleConfidence = when {
            cycleCount >= 12 -> 1.0
            cycleCount >= 6 -> 0.8
            cycleCount >= 3 -> 0.6
            else -> 0.4
        }
        
        val regularityConfidence = when {
            variability < 3.0 -> 1.0
            variability < 7.0 -> 0.8
            variability < 14.0 -> 0.6
            else -> 0.4
        }
        
        return (cycleConfidence + regularityConfidence) / 2.0
    }
    
    private fun createBasicPrediction(
        nextStart: LocalDate,
        userProfile: UserProfile,
        confidence: Double
    ): CyclePrediction {
        return CyclePrediction(
            nextPeriodStart = nextStart,
            nextPeriodEnd = nextStart.plusDays((userProfile.averagePeriodLength - 1).toLong()),
            nextFertileWindowStart = nextStart.plusDays((userProfile.averageCycleLength / 2 - 2).toLong()),
            nextFertileWindowEnd = nextStart.plusDays((userProfile.averageCycleLength / 2 + 2).toLong()),
            nextPMSStart = nextStart.plusDays((userProfile.averageCycleLength - 7).toLong()),
            confidence = confidence,
            basedOnCycles = 0
        )
    }
}
