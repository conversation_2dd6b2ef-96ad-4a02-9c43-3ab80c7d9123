package com.Hamode.periodpal.data.models

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

/**
 * Represents a complete menstrual cycle
 */
data class MenstrualCycle(
    val id: String = "",
    val startDate: LocalDate,
    val endDate: LocalDate? = null, // null if cycle is ongoing
    val dailyLogs: List<DailyLog> = emptyList(),
    val cycleLength: Int? = null, // calculated when cycle ends
    val periodLength: Int? = null, // number of days with flow
    val isComplete: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    /**
     * Gets the current cycle day (1-based)
     */
    fun getCurrentCycleDay(currentDate: LocalDate = LocalDate.now()): Int {
        return ChronoUnit.DAYS.between(startDate, currentDate).toInt() + 1
    }
    
    /**
     * Gets the actual cycle length if complete
     */
    fun getActualCycleLength(): Int? {
        return endDate?.let { 
            ChronoUnit.DAYS.between(startDate, it).toInt() + 1 
        }
    }
    
    /**
     * Gets the actual period length (days with flow)
     */
    fun getActualPeriodLength(): Int {
        return dailyLogs.count { it.isPeriodDay() }
    }
    
    /**
     * Gets the current cycle phase based on cycle day
     */
    fun getCurrentPhase(currentDate: LocalDate = LocalDate.now()): CyclePhase {
        val cycleDay = getCurrentCycleDay(currentDate)
        val estimatedLength = cycleLength ?: 28 // default to 28 days
        return CyclePhase.fromCycleDay(cycleDay, estimatedLength)
    }
    
    /**
     * Gets predicted fertile window (ovulation ± 2 days)
     */
    fun getFertileWindow(): Pair<LocalDate, LocalDate>? {
        val estimatedLength = cycleLength ?: 28
        val ovulationDay = estimatedLength / 2
        val fertileStart = startDate.plusDays((ovulationDay - 3).toLong())
        val fertileEnd = startDate.plusDays((ovulationDay + 1).toLong())
        return Pair(fertileStart, fertileEnd)
    }
    
    /**
     * Gets predicted PMS window (last 7 days of cycle)
     */
    fun getPMSWindow(): Pair<LocalDate, LocalDate>? {
        val estimatedLength = cycleLength ?: 28
        val pmsStart = startDate.plusDays((estimatedLength - 7).toLong())
        val pmsEnd = startDate.plusDays((estimatedLength - 1).toLong())
        return Pair(pmsStart, pmsEnd)
    }
    
    /**
     * Gets daily log for a specific date
     */
    fun getDailyLog(date: LocalDate): DailyLog? {
        return dailyLogs.find { it.date == date }
    }
    
    /**
     * Checks if a date falls within this cycle
     */
    fun containsDate(date: LocalDate): Boolean {
        return if (endDate != null) {
            !date.isBefore(startDate) && !date.isAfter(endDate)
        } else {
            !date.isBefore(startDate)
        }
    }
}
