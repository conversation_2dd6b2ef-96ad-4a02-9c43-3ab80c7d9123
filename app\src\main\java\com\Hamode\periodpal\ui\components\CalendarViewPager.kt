package com.Hamode.periodpal.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarMonth
import androidx.compose.material.icons.filled.RadioButtonUnchecked
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.CyclePhase
import com.Hamode.periodpal.data.models.FlowIntensity
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.YearMonth

enum class CalendarViewType {
    CIRCULAR,
    MONTHLY
}

@Composable
fun CalendarViewPager(
    currentDate: LocalDate,
    cycleDay: Int,
    phase: CyclePhase,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier,
    cycleStartDate: LocalDate = LocalDate.now().minusDays(cycleDay.toLong() - 1),
    fertileWindow: Pair<LocalDate, LocalDate>? = null,
    pmsWindow: Pair<LocalDate, LocalDate>? = null,
    periodDays: List<LocalDate> = emptyList(),
    nextPeriodPrediction: LocalDate? = null,
    calendarData: Map<LocalDate, CalendarDayData> = emptyMap()
) {
    var selectedView by remember { mutableStateOf(CalendarViewType.CIRCULAR) }
    var selectedMonth by remember { mutableStateOf(YearMonth.from(currentDate)) }
    
    Column(modifier = modifier) {
        // View Toggle
        CalendarViewToggle(
            selectedView = selectedView,
            onViewChanged = { selectedView = it },
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Calendar Content
        when (selectedView) {
            CalendarViewType.CIRCULAR -> {
                CircularCalendarWidget(
                    currentDate = currentDate,
                    cycleDay = cycleDay,
                    phase = phase,
                    onDateSelected = onDateSelected,
                    cycleStartDate = cycleStartDate,
                    fertileWindow = fertileWindow,
                    pmsWindow = pmsWindow,
                    periodDays = periodDays,
                    nextPeriodPrediction = nextPeriodPrediction,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            CalendarViewType.MONTHLY -> {
                MonthlyCalendarWidget(
                    selectedMonth = selectedMonth,
                    onMonthChanged = { selectedMonth = it },
                    onDateSelected = onDateSelected,
                    calendarData = calendarData,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Calendar Insights
        CalendarInsights(
            selectedView = selectedView,
            currentDate = currentDate,
            cycleDay = cycleDay,
            phase = phase,
            fertileWindow = fertileWindow,
            nextPeriodPrediction = nextPeriodPrediction,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun CalendarViewToggle(
    selectedView: CalendarViewType,
    onViewChanged: (CalendarViewType) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = LightGray.copy(alpha = 0.3f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier.padding(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            CalendarViewType.values().forEach { viewType ->
                val isSelected = selectedView == viewType
                val backgroundColor = if (isSelected) {
                    MaterialTheme.colorScheme.surface
                } else {
                    Color.Transparent
                }
                
                val textColor = if (isSelected) {
                    RosePink40
                } else {
                    DarkGray.copy(alpha = 0.7f)
                }
                
                Row(
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            color = backgroundColor,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = when (viewType) {
                            CalendarViewType.CIRCULAR -> Icons.Default.RadioButtonUnchecked
                            CalendarViewType.MONTHLY -> Icons.Default.CalendarMonth
                        },
                        contentDescription = viewType.name,
                        tint = textColor,
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = when (viewType) {
                            CalendarViewType.CIRCULAR -> "Cycle View"
                            CalendarViewType.MONTHLY -> "Monthly View"
                        },
                        style = MaterialTheme.typography.labelMedium,
                        color = textColor,
                        fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                    )
                }
            }
        }
    }
}

@Composable
private fun CalendarInsights(
    selectedView: CalendarViewType,
    currentDate: LocalDate,
    cycleDay: Int,
    phase: CyclePhase,
    fertileWindow: Pair<LocalDate, LocalDate>?,
    nextPeriodPrediction: LocalDate?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = SoftPink80.copy(alpha = 0.3f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Today's Insights",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Current Phase Info
                InsightItem(
                    title = "Current Phase",
                    value = phase.displayName,
                    color = PeriodPalThemeColors.getPhaseColor(phase),
                    modifier = Modifier.weight(1f)
                )
                
                // Days until next period
                nextPeriodPrediction?.let { prediction ->
                    val daysUntil = java.time.temporal.ChronoUnit.DAYS.between(currentDate, prediction).toInt()
                    InsightItem(
                        title = "Next Period",
                        value = if (daysUntil > 0) "$daysUntil days" else "Today",
                        color = PeriodRed,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                // Fertile window status
                fertileWindow?.let { (start, end) ->
                    val isInWindow = !currentDate.isBefore(start) && !currentDate.isAfter(end)
                    InsightItem(
                        title = "Fertility",
                        value = if (isInWindow) "High" else "Low",
                        color = if (isInWindow) FertileGreen else DarkGray.copy(alpha = 0.5f),
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun InsightItem(
    title: String,
    value: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            color = color,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Preview(showBackground = true, name = "Calendar View Pager Preview")
@Composable
fun CalendarViewPagerPreview() {
    val today = LocalDate.now()
    val cycleStart = today.minusDays(14)
    
    val sampleCalendarData = mapOf(
        today to CalendarDayData(
            date = today,
            isToday = true,
            cycleDay = 15,
            phase = CyclePhase.OVULATION,
            isFertile = true
        ),
        today.minusDays(10) to CalendarDayData(
            date = today.minusDays(10),
            flowIntensity = FlowIntensity.MEDIUM,
            cycleDay = 5
        )
    )
    
    PeriodPalTheme {
        CalendarViewPager(
            currentDate = today,
            cycleDay = 15,
            phase = CyclePhase.OVULATION,
            onDateSelected = { },
            cycleStartDate = cycleStart,
            fertileWindow = Pair(today.minusDays(2), today.plusDays(2)),
            pmsWindow = Pair(today.plusDays(10), today.plusDays(16)),
            periodDays = listOf(
                cycleStart,
                cycleStart.plusDays(1),
                cycleStart.plusDays(2)
            ),
            nextPeriodPrediction = today.plusDays(14),
            calendarData = sampleCalendarData
        )
    }
}
