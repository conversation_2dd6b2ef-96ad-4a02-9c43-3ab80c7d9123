package com.Hamode.periodpal.data.models

import java.time.LocalDate

/**
 * Data class representing calendar day information for the period tracking calendar
 */
data class CalendarDayData(
    val date: LocalDate,
    val isToday: Boolean = false,
    val cycleDay: Int? = null,
    val phase: CyclePhase? = null,
    val flowIntensity: FlowIntensity = FlowIntensity.NONE,
    val hasSymptoms: Boolean = false,
    val symptoms: List<SymptomType> = emptyList(),
    val moods: List<MoodType> = emptyList(),
    val painLevel: PainLevel = PainLevel.NONE,
    val isFertile: Boolean = false,
    val isPMS: Boolean = false,
    val isPredicted: Boolean = false,
    val notes: String = "",
    val temperature: Double? = null,
    val weight: Double? = null
) {
    /**
     * Checks if this is a period day
     */
    fun isPeriodDay(): Boolean {
        return flowIntensity != FlowIntensity.NONE
    }
    
    /**
     * Checks if this day has any logged data
     */
    fun hasData(): <PERSON>ole<PERSON> {
        return isPeriodDay() || hasSymptoms || moods.isNotEmpty() || 
               painLevel != PainLevel.NONE || notes.isNotEmpty() ||
               temperature != null || weight != null
    }
    
    /**
     * Gets the primary indicator for this day (for calendar display)
     */
    fun getPrimaryIndicator(): CalendarIndicator {
        return when {
            isPeriodDay() -> CalendarIndicator.PERIOD
            isFertile -> CalendarIndicator.FERTILE
            isPMS -> CalendarIndicator.PMS
            hasSymptoms -> CalendarIndicator.SYMPTOMS
            moods.isNotEmpty() -> CalendarIndicator.MOOD
            else -> CalendarIndicator.NONE
        }
    }
    
    /**
     * Gets a summary description for accessibility
     */
    fun getAccessibilityDescription(): String {
        val parts = mutableListOf<String>()
        
        // Add date
        parts.add(date.format(java.time.format.DateTimeFormatter.ofPattern("EEEE, MMMM dd")))
        
        // Add cycle information
        cycleDay?.let { parts.add("Cycle day $it") }
        phase?.let { parts.add(it.displayName) }
        
        // Add flow information
        if (isPeriodDay()) {
            parts.add("Period day with ${flowIntensity.displayName.lowercase()} flow")
        }
        
        // Add fertility information
        if (isFertile) {
            parts.add("Fertile window")
        }
        
        // Add PMS information
        if (isPMS) {
            parts.add("PMS period")
        }
        
        // Add symptoms
        if (hasSymptoms) {
            parts.add("Has ${symptoms.size} logged symptoms")
        }
        
        // Add mood
        if (moods.isNotEmpty()) {
            parts.add("Mood: ${moods.first().displayName}")
        }
        
        // Add pain level
        if (painLevel != PainLevel.NONE) {
            parts.add("Pain level: ${painLevel.displayName}")
        }
        
        // Add notes
        if (notes.isNotEmpty()) {
            parts.add("Has notes")
        }
        
        return parts.joinToString(". ")
    }
}

/**
 * Enum representing different calendar day indicators
 */
enum class CalendarIndicator {
    NONE,
    PERIOD,
    FERTILE,
    PMS,
    SYMPTOMS,
    MOOD
}

/**
 * Extension functions for CalendarDayData
 */
fun CalendarDayData.getDisplayColor(): androidx.compose.ui.graphics.Color {
    return when (getPrimaryIndicator()) {
        CalendarIndicator.PERIOD -> com.Hamode.periodpal.ui.theme.PeriodRed
        CalendarIndicator.FERTILE -> com.Hamode.periodpal.ui.theme.FertileGreen
        CalendarIndicator.PMS -> com.Hamode.periodpal.ui.theme.PMSPurple
        CalendarIndicator.SYMPTOMS -> com.Hamode.periodpal.ui.theme.WarningOrange
        CalendarIndicator.MOOD -> com.Hamode.periodpal.ui.theme.InfoBlue
        CalendarIndicator.NONE -> androidx.compose.ui.graphics.Color.Transparent
    }
}

/**
 * Helper function to create calendar data for a range of dates
 */
fun createCalendarDataRange(
    startDate: LocalDate,
    endDate: LocalDate,
    cycleStartDate: LocalDate? = null,
    averageCycleLength: Int = 28
): Map<LocalDate, CalendarDayData> {
    val dataMap = mutableMapOf<LocalDate, CalendarDayData>()
    var currentDate = startDate
    
    while (!currentDate.isAfter(endDate)) {
        val cycleDay = cycleStartDate?.let { 
            java.time.temporal.ChronoUnit.DAYS.between(it, currentDate).toInt() + 1
        }
        
        val phase = cycleDay?.let { day ->
            when {
                day <= 5 -> CyclePhase.MENSTRUAL
                day <= 13 -> CyclePhase.FOLLICULAR
                day <= 16 -> CyclePhase.OVULATION
                else -> CyclePhase.LUTEAL
            }
        }
        
        dataMap[currentDate] = CalendarDayData(
            date = currentDate,
            isToday = currentDate == LocalDate.now(),
            cycleDay = cycleDay,
            phase = phase,
            isFertile = cycleDay?.let { it in 12..16 } ?: false,
            isPMS = cycleDay?.let { it > averageCycleLength - 7 } ?: false
        )
        
        currentDate = currentDate.plusDays(1)
    }
    
    return dataMap
}
