  Activity android.app  PeriodPalApp android.app.Activity  PeriodPalTheme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  Context android.content  PeriodPalApp android.content.Context  PeriodPalTheme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  PeriodPalApp android.content.ContextWrapper  PeriodPalTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  PeriodPalApp  android.view.ContextThemeWrapper  PeriodPalTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  isInEditMode android.view.View  statusBarColor android.view.Window  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  PeriodPalApp #androidx.activity.ComponentActivity  PeriodPalTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Add "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  	Analytics "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
Assignment "androidx.compose.foundation.layout  Badge "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  
BottomNavItem "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  CalendarDay "androidx.compose.foundation.layout  CalendarGrid "androidx.compose.foundation.layout  CalendarHeader "androidx.compose.foundation.layout  CalendarLegend "androidx.compose.foundation.layout  
CalendarMonth "androidx.compose.foundation.layout  
CalendarToday "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  ChevronLeft "androidx.compose.foundation.layout  ChevronRight "androidx.compose.foundation.layout  Circle "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CycleOverviewCard "androidx.compose.foundation.layout  
CyclePhase "androidx.compose.foundation.layout  DarkGray "androidx.compose.foundation.layout  DateTimeFormatter "androidx.compose.foundation.layout  FertileGreen "androidx.compose.foundation.layout  
FlowIntensity "androidx.compose.foundation.layout  FlowIntensityButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  
HeaderSection "androidx.compose.foundation.layout  HealthInsightsPreview "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  
LegendItem "androidx.compose.foundation.layout  	LightGray "androidx.compose.foundation.layout  	LocalDate "androidx.compose.foundation.layout  Map "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  NavigationDestination "androidx.compose.foundation.layout  
Notifications "androidx.compose.foundation.layout  OverviewItem "androidx.compose.foundation.layout  	PMSPurple "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PeriodPalApp "androidx.compose.foundation.layout  PeriodPalTheme "androidx.compose.foundation.layout  PeriodPalThemeColors "androidx.compose.foundation.layout  	PeriodRed "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  QuickActionButton "androidx.compose.foundation.layout  QuickActionButtons "androidx.compose.foundation.layout  
RosePink40 "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Schedule "androidx.compose.foundation.layout  SelectedDateInfo "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  
SoftPink40 "androidx.compose.foundation.layout  
SoftPink80 "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Stop "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SuccessGreen "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TodayLoggingSection "androidx.compose.foundation.layout  
TrendingUp "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  White "androidx.compose.foundation.layout  	YearMonth "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  emptyMap "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getFlowColor "androidx.compose.foundation.layout  
getPhaseColor "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  lightColorScheme "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Badge +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  com +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Badge .androidx.compose.foundation.layout.ColumnScope  
BottomNavItem .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  CalendarDay .androidx.compose.foundation.layout.ColumnScope  CalendarGrid .androidx.compose.foundation.layout.ColumnScope  CalendarHeader .androidx.compose.foundation.layout.ColumnScope  CalendarLegend .androidx.compose.foundation.layout.ColumnScope  
CalendarToday .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  ChevronLeft .androidx.compose.foundation.layout.ColumnScope  ChevronRight .androidx.compose.foundation.layout.ColumnScope  Circle .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  CycleOverviewCard .androidx.compose.foundation.layout.ColumnScope  
CyclePhase .androidx.compose.foundation.layout.ColumnScope  DarkGray .androidx.compose.foundation.layout.ColumnScope  DateTimeFormatter .androidx.compose.foundation.layout.ColumnScope  FertileGreen .androidx.compose.foundation.layout.ColumnScope  
FlowIntensity .androidx.compose.foundation.layout.ColumnScope  FlowIntensityButton .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  
HeaderSection .androidx.compose.foundation.layout.ColumnScope  HealthInsightsPreview .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  
LegendItem .androidx.compose.foundation.layout.ColumnScope  	LocalDate .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  NavigationDestination .androidx.compose.foundation.layout.ColumnScope  
Notifications .androidx.compose.foundation.layout.ColumnScope  OverviewItem .androidx.compose.foundation.layout.ColumnScope  	PMSPurple .androidx.compose.foundation.layout.ColumnScope  PeriodPalThemeColors .androidx.compose.foundation.layout.ColumnScope  	PeriodRed .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  QuickActionButton .androidx.compose.foundation.layout.ColumnScope  QuickActionButtons .androidx.compose.foundation.layout.ColumnScope  
RosePink40 .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Schedule .androidx.compose.foundation.layout.ColumnScope  SelectedDateInfo .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  
SoftPink40 .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Stop .androidx.compose.foundation.layout.ColumnScope  SuccessGreen .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TodayLoggingSection .androidx.compose.foundation.layout.ColumnScope  
TrendingUp .androidx.compose.foundation.layout.ColumnScope  White .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  com .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  
getPhaseColor .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  
BottomNavItem +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  
CalendarToday +androidx.compose.foundation.layout.RowScope  ChevronLeft +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  Circle +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  DarkGray +androidx.compose.foundation.layout.RowScope  DateTimeFormatter +androidx.compose.foundation.layout.RowScope  FertileGreen +androidx.compose.foundation.layout.RowScope  
FlowIntensity +androidx.compose.foundation.layout.RowScope  FlowIntensityButton +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
LegendItem +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationDestination +androidx.compose.foundation.layout.RowScope  
Notifications +androidx.compose.foundation.layout.RowScope  OverviewItem +androidx.compose.foundation.layout.RowScope  	PMSPurple +androidx.compose.foundation.layout.RowScope  PeriodPalThemeColors +androidx.compose.foundation.layout.RowScope  	PeriodRed +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  QuickActionButton +androidx.compose.foundation.layout.RowScope  
RosePink40 +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Schedule +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  
SoftPink40 +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Stop +androidx.compose.foundation.layout.RowScope  SuccessGreen +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  
TrendingUp +androidx.compose.foundation.layout.RowScope  White +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  
getPhaseColor +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  Color ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  Box 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  CalendarDay 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	LocalDate 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Modifier 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  dp 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  size 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Box 3androidx.compose.foundation.lazy.grid.LazyGridScope  CalendarDay 3androidx.compose.foundation.lazy.grid.LazyGridScope  	LocalDate 3androidx.compose.foundation.lazy.grid.LazyGridScope  Modifier 3androidx.compose.foundation.lazy.grid.LazyGridScope  dp 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  size 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	Analytics ,androidx.compose.material.icons.Icons.Filled  
Assignment ,androidx.compose.material.icons.Icons.Filled  
CalendarMonth ,androidx.compose.material.icons.Icons.Filled  
CalendarToday ,androidx.compose.material.icons.Icons.Filled  ChevronLeft ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  Circle ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Schedule ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Stop ,androidx.compose.material.icons.Icons.Filled  
TrendingUp ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	Analytics &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  
Assignment &androidx.compose.material.icons.filled  Badge &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  
BottomNavItem &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Brush &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  CalendarDay &androidx.compose.material.icons.filled  CalendarGrid &androidx.compose.material.icons.filled  CalendarHeader &androidx.compose.material.icons.filled  CalendarLegend &androidx.compose.material.icons.filled  
CalendarMonth &androidx.compose.material.icons.filled  
CalendarToday &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  ChevronLeft &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  Circle &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  CycleOverviewCard &androidx.compose.material.icons.filled  
CyclePhase &androidx.compose.material.icons.filled  DarkGray &androidx.compose.material.icons.filled  DateTimeFormatter &androidx.compose.material.icons.filled  FertileGreen &androidx.compose.material.icons.filled  
FlowIntensity &androidx.compose.material.icons.filled  FlowIntensityButton &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  	GridCells &androidx.compose.material.icons.filled  
HeaderSection &androidx.compose.material.icons.filled  HealthInsightsPreview &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  LazyVerticalGrid &androidx.compose.material.icons.filled  
LegendItem &androidx.compose.material.icons.filled  	LightGray &androidx.compose.material.icons.filled  	LocalDate &androidx.compose.material.icons.filled  Map &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  NavigationDestination &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  OverviewItem &androidx.compose.material.icons.filled  	PMSPurple &androidx.compose.material.icons.filled  PeriodPalThemeColors &androidx.compose.material.icons.filled  	PeriodRed &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  QuickActionButton &androidx.compose.material.icons.filled  QuickActionButtons &androidx.compose.material.icons.filled  
RosePink40 &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  SelectedDateInfo &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  
SoftPink40 &androidx.compose.material.icons.filled  
SoftPink80 &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Stop &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  SuccessGreen &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  TodayLoggingSection &androidx.compose.material.icons.filled  
TrendingUp &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  White &androidx.compose.material.icons.filled  	YearMonth &androidx.compose.material.icons.filled  align &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  
cardElevation &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  emptyMap &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  getFlowColor &androidx.compose.material.icons.filled  
getPhaseColor &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberScrollState &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  verticalGradient &androidx.compose.material.icons.filled  verticalScroll &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  Color Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  Add androidx.compose.material3  	Alignment androidx.compose.material3  	Analytics androidx.compose.material3  Arrangement androidx.compose.material3  
Assignment androidx.compose.material3  Badge androidx.compose.material3  Boolean androidx.compose.material3  
BottomNavItem androidx.compose.material3  Box androidx.compose.material3  Brush androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  CalendarDay androidx.compose.material3  CalendarGrid androidx.compose.material3  CalendarHeader androidx.compose.material3  CalendarLegend androidx.compose.material3  
CalendarMonth androidx.compose.material3  
CalendarToday androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ChevronLeft androidx.compose.material3  ChevronRight androidx.compose.material3  Circle androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  CycleOverviewCard androidx.compose.material3  
CyclePhase androidx.compose.material3  DarkGray androidx.compose.material3  DateTimeFormatter androidx.compose.material3  FertileGreen androidx.compose.material3  
FlowIntensity androidx.compose.material3  FlowIntensityButton androidx.compose.material3  
FontWeight androidx.compose.material3  	GridCells androidx.compose.material3  
HeaderSection androidx.compose.material3  HealthInsightsPreview androidx.compose.material3  Home androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  Int androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  
LegendItem androidx.compose.material3  	LightGray androidx.compose.material3  	LocalDate androidx.compose.material3  Map androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  NavigationDestination androidx.compose.material3  
Notifications androidx.compose.material3  OverviewItem androidx.compose.material3  	PMSPurple androidx.compose.material3  PeriodPalApp androidx.compose.material3  PeriodPalTheme androidx.compose.material3  PeriodPalThemeColors androidx.compose.material3  	PeriodRed androidx.compose.material3  Person androidx.compose.material3  	PlayArrow androidx.compose.material3  Preview androidx.compose.material3  QuickActionButton androidx.compose.material3  QuickActionButtons androidx.compose.material3  
RosePink40 androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Schedule androidx.compose.material3  SelectedDateInfo androidx.compose.material3  Settings androidx.compose.material3  
SoftPink40 androidx.compose.material3  
SoftPink80 androidx.compose.material3  Spacer androidx.compose.material3  Stop androidx.compose.material3  String androidx.compose.material3  SuccessGreen androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TodayLoggingSection androidx.compose.material3  
TrendingUp androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  White androidx.compose.material3  	YearMonth androidx.compose.material3  align androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  clip androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  emptyMap androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getFlowColor androidx.compose.material3  
getPhaseColor androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberScrollState androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  verticalGradient androidx.compose.material3  verticalScroll androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  Color 7androidx.compose.material3.androidx.compose.ui.graphics  Add androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Brush androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  CalendarDay androidx.compose.runtime  CalendarGrid androidx.compose.runtime  CalendarHeader androidx.compose.runtime  CalendarLegend androidx.compose.runtime  
CalendarToday androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  ChevronLeft androidx.compose.runtime  ChevronRight androidx.compose.runtime  Circle androidx.compose.runtime  CircleShape androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  CycleOverviewCard androidx.compose.runtime  
CyclePhase androidx.compose.runtime  DarkGray androidx.compose.runtime  DateTimeFormatter androidx.compose.runtime  FertileGreen androidx.compose.runtime  
FlowIntensity androidx.compose.runtime  FlowIntensityButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  
HeaderSection androidx.compose.runtime  HealthInsightsPreview androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  Int androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  
LegendItem androidx.compose.runtime  	LightGray androidx.compose.runtime  	LocalDate androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  
Notifications androidx.compose.runtime  OverviewItem androidx.compose.runtime  	PMSPurple androidx.compose.runtime  PeriodPalApp androidx.compose.runtime  PeriodPalTheme androidx.compose.runtime  PeriodPalThemeColors androidx.compose.runtime  	PeriodRed androidx.compose.runtime  	PlayArrow androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  QuickActionButton androidx.compose.runtime  QuickActionButtons androidx.compose.runtime  
RosePink40 androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Schedule androidx.compose.runtime  SelectedDateInfo androidx.compose.runtime  Settings androidx.compose.runtime  
SideEffect androidx.compose.runtime  
SoftPink40 androidx.compose.runtime  
SoftPink80 androidx.compose.runtime  Spacer androidx.compose.runtime  Stop androidx.compose.runtime  String androidx.compose.runtime  SuccessGreen androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TodayLoggingSection androidx.compose.runtime  
TrendingUp androidx.compose.runtime  Unit androidx.compose.runtime  White androidx.compose.runtime  	YearMonth androidx.compose.runtime  androidx androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  clip androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getFlowColor androidx.compose.runtime  
getPhaseColor androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  lightColorScheme androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberScrollState androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  verticalGradient androidx.compose.runtime  verticalScroll androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  Color 5androidx.compose.runtime.androidx.compose.ui.graphics  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  PeriodPalApp #androidx.core.app.ComponentActivity  PeriodPalTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  	Alignment com.Hamode.periodpal  Arrangement com.Hamode.periodpal  Box com.Hamode.periodpal  Brush com.Hamode.periodpal  Bundle com.Hamode.periodpal  Button com.Hamode.periodpal  ButtonDefaults com.Hamode.periodpal  Card com.Hamode.periodpal  CardDefaults com.Hamode.periodpal  Color com.Hamode.periodpal  Column com.Hamode.periodpal  ComponentActivity com.Hamode.periodpal  
Composable com.Hamode.periodpal  
FontWeight com.Hamode.periodpal  MainActivity com.Hamode.periodpal  
MaterialTheme com.Hamode.periodpal  Modifier com.Hamode.periodpal  PeriodPalApp com.Hamode.periodpal  PeriodPalMainScreen com.Hamode.periodpal  PeriodPalMainScreenPreview com.Hamode.periodpal  PeriodPalSimpleTheme com.Hamode.periodpal  PeriodPalTheme com.Hamode.periodpal  PlaceholderScreen com.Hamode.periodpal  Preview com.Hamode.periodpal  RoundedCornerShape com.Hamode.periodpal  Scaffold com.Hamode.periodpal  Spacer com.Hamode.periodpal  String com.Hamode.periodpal  Text com.Hamode.periodpal  	TextAlign com.Hamode.periodpal  Unit com.Hamode.periodpal  androidx com.Hamode.periodpal  buttonColors com.Hamode.periodpal  
cardColors com.Hamode.periodpal  
cardElevation com.Hamode.periodpal  com com.Hamode.periodpal  fillMaxSize com.Hamode.periodpal  fillMaxWidth com.Hamode.periodpal  getValue com.Hamode.periodpal  height com.Hamode.periodpal  lightColorScheme com.Hamode.periodpal  listOf com.Hamode.periodpal  mutableStateOf com.Hamode.periodpal  padding com.Hamode.periodpal  provideDelegate com.Hamode.periodpal  remember com.Hamode.periodpal  setValue com.Hamode.periodpal  spacedBy com.Hamode.periodpal  verticalGradient com.Hamode.periodpal  PeriodPalApp !com.Hamode.periodpal.MainActivity  PeriodPalTheme !com.Hamode.periodpal.MainActivity  enableEdgeToEdge !com.Hamode.periodpal.MainActivity  
setContent !com.Hamode.periodpal.MainActivity  Add  com.Hamode.periodpal.data.models  	Alignment  com.Hamode.periodpal.data.models  Arrangement  com.Hamode.periodpal.data.models  Boolean  com.Hamode.periodpal.data.models  Box  com.Hamode.periodpal.data.models  Brush  com.Hamode.periodpal.data.models  Button  com.Hamode.periodpal.data.models  ButtonDefaults  com.Hamode.periodpal.data.models  CalendarDay  com.Hamode.periodpal.data.models  CalendarDayData  com.Hamode.periodpal.data.models  CalendarGrid  com.Hamode.periodpal.data.models  CalendarHeader  com.Hamode.periodpal.data.models  CalendarLegend  com.Hamode.periodpal.data.models  
CalendarToday  com.Hamode.periodpal.data.models  Card  com.Hamode.periodpal.data.models  CardDefaults  com.Hamode.periodpal.data.models  ChevronLeft  com.Hamode.periodpal.data.models  ChevronRight  com.Hamode.periodpal.data.models  Circle  com.Hamode.periodpal.data.models  CircleShape  com.Hamode.periodpal.data.models  Color  com.Hamode.periodpal.data.models  Column  com.Hamode.periodpal.data.models  
Composable  com.Hamode.periodpal.data.models  CycleOverviewCard  com.Hamode.periodpal.data.models  
CyclePhase  com.Hamode.periodpal.data.models  CyclePrediction  com.Hamode.periodpal.data.models  CycleStatistics  com.Hamode.periodpal.data.models  DailyLog  com.Hamode.periodpal.data.models  DarkGray  com.Hamode.periodpal.data.models  DateTimeFormatter  com.Hamode.periodpal.data.models  Double  com.Hamode.periodpal.data.models  FertileGreen  com.Hamode.periodpal.data.models  
FlowIntensity  com.Hamode.periodpal.data.models  FlowIntensityButton  com.Hamode.periodpal.data.models  
FontWeight  com.Hamode.periodpal.data.models  	GridCells  com.Hamode.periodpal.data.models  
HeaderSection  com.Hamode.periodpal.data.models  
HealthInsight  com.Hamode.periodpal.data.models  HealthInsightSeverity  com.Hamode.periodpal.data.models  HealthInsightType  com.Hamode.periodpal.data.models  HealthInsightsPreview  com.Hamode.periodpal.data.models  Icon  com.Hamode.periodpal.data.models  
IconButton  com.Hamode.periodpal.data.models  Icons  com.Hamode.periodpal.data.models  ImageVector  com.Hamode.periodpal.data.models  Int  com.Hamode.periodpal.data.models  LazyVerticalGrid  com.Hamode.periodpal.data.models  
LegendItem  com.Hamode.periodpal.data.models  	LightGray  com.Hamode.periodpal.data.models  List  com.Hamode.periodpal.data.models  	LocalDate  com.Hamode.periodpal.data.models  Map  com.Hamode.periodpal.data.models  
MaterialTheme  com.Hamode.periodpal.data.models  Modifier  com.Hamode.periodpal.data.models  	MoodState  com.Hamode.periodpal.data.models  
Notifications  com.Hamode.periodpal.data.models  OverviewItem  com.Hamode.periodpal.data.models  	PMSPurple  com.Hamode.periodpal.data.models  	PainLevel  com.Hamode.periodpal.data.models  PeriodPalThemeColors  com.Hamode.periodpal.data.models  	PeriodRed  com.Hamode.periodpal.data.models  	PlayArrow  com.Hamode.periodpal.data.models  QuickActionButton  com.Hamode.periodpal.data.models  QuickActionButtons  com.Hamode.periodpal.data.models  
RosePink40  com.Hamode.periodpal.data.models  Row  com.Hamode.periodpal.data.models  Schedule  com.Hamode.periodpal.data.models  SelectedDateInfo  com.Hamode.periodpal.data.models  Settings  com.Hamode.periodpal.data.models  
SoftPink40  com.Hamode.periodpal.data.models  
SoftPink80  com.Hamode.periodpal.data.models  Spacer  com.Hamode.periodpal.data.models  Stop  com.Hamode.periodpal.data.models  String  com.Hamode.periodpal.data.models  SuccessGreen  com.Hamode.periodpal.data.models  Symptom  com.Hamode.periodpal.data.models  Text  com.Hamode.periodpal.data.models  	TextAlign  com.Hamode.periodpal.data.models  
TextButton  com.Hamode.periodpal.data.models  TodayLoggingSection  com.Hamode.periodpal.data.models  
TrendingUp  com.Hamode.periodpal.data.models  Unit  com.Hamode.periodpal.data.models  White  com.Hamode.periodpal.data.models  	YearMonth  com.Hamode.periodpal.data.models  androidx  com.Hamode.periodpal.data.models  
background  com.Hamode.periodpal.data.models  buttonColors  com.Hamode.periodpal.data.models  
cardColors  com.Hamode.periodpal.data.models  
cardElevation  com.Hamode.periodpal.data.models  clip  com.Hamode.periodpal.data.models  	emptyList  com.Hamode.periodpal.data.models  fillMaxSize  com.Hamode.periodpal.data.models  fillMaxWidth  com.Hamode.periodpal.data.models  forEach  com.Hamode.periodpal.data.models  getFlowColor  com.Hamode.periodpal.data.models  
getPhaseColor  com.Hamode.periodpal.data.models  getValue  com.Hamode.periodpal.data.models  height  com.Hamode.periodpal.data.models  listOf  com.Hamode.periodpal.data.models  mutableStateOf  com.Hamode.periodpal.data.models  padding  com.Hamode.periodpal.data.models  provideDelegate  com.Hamode.periodpal.data.models  remember  com.Hamode.periodpal.data.models  rememberScrollState  com.Hamode.periodpal.data.models  setValue  com.Hamode.periodpal.data.models  size  com.Hamode.periodpal.data.models  spacedBy  com.Hamode.periodpal.data.models  verticalGradient  com.Hamode.periodpal.data.models  verticalScroll  com.Hamode.periodpal.data.models  weight  com.Hamode.periodpal.data.models  width  com.Hamode.periodpal.data.models  
FOLLICULAR +com.Hamode.periodpal.data.models.CyclePhase  LUTEAL +com.Hamode.periodpal.data.models.CyclePhase  	MENSTRUAL +com.Hamode.periodpal.data.models.CyclePhase  	OVULATION +com.Hamode.periodpal.data.models.CyclePhase  displayName +com.Hamode.periodpal.data.models.CyclePhase  cycleVariability 0com.Hamode.periodpal.data.models.CycleStatistics  HEAVY .com.Hamode.periodpal.data.models.FlowIntensity  LIGHT .com.Hamode.periodpal.data.models.FlowIntensity  MEDIUM .com.Hamode.periodpal.data.models.FlowIntensity  NONE .com.Hamode.periodpal.data.models.FlowIntensity  SPOTTING .com.Hamode.periodpal.data.models.FlowIntensity  
VERY_HEAVY .com.Hamode.periodpal.data.models.FlowIntensity  level .com.Hamode.periodpal.data.models.FlowIntensity  values .com.Hamode.periodpal.data.models.FlowIntensity  ANXIOUS *com.Hamode.periodpal.data.models.MoodState  CALM *com.Hamode.periodpal.data.models.MoodState  	EMOTIONAL *com.Hamode.periodpal.data.models.MoodState  	ENERGETIC *com.Hamode.periodpal.data.models.MoodState  HAPPY *com.Hamode.periodpal.data.models.MoodState  	IRRITABLE *com.Hamode.periodpal.data.models.MoodState  SAD *com.Hamode.periodpal.data.models.MoodState  TIRED *com.Hamode.periodpal.data.models.MoodState  EXTREME *com.Hamode.periodpal.data.models.PainLevel  MILD *com.Hamode.periodpal.data.models.PainLevel  MODERATE *com.Hamode.periodpal.data.models.PainLevel  NONE *com.Hamode.periodpal.data.models.PainLevel  SEVERE *com.Hamode.periodpal.data.models.PainLevel  compose )com.Hamode.periodpal.data.models.androidx  ui 1com.Hamode.periodpal.data.models.androidx.compose  graphics 4com.Hamode.periodpal.data.models.androidx.compose.ui  Color =com.Hamode.periodpal.data.models.androidx.compose.ui.graphics  	Alignment "com.Hamode.periodpal.ui.components  	Analytics "com.Hamode.periodpal.ui.components  Arrangement "com.Hamode.periodpal.ui.components  
Assignment "com.Hamode.periodpal.ui.components  Badge "com.Hamode.periodpal.ui.components  Boolean "com.Hamode.periodpal.ui.components  
BottomNavItem "com.Hamode.periodpal.ui.components  Box "com.Hamode.periodpal.ui.components  
CalendarMonth "com.Hamode.periodpal.ui.components  Card "com.Hamode.periodpal.ui.components  CardDefaults "com.Hamode.periodpal.ui.components  Column "com.Hamode.periodpal.ui.components  
Composable "com.Hamode.periodpal.ui.components  DarkGray "com.Hamode.periodpal.ui.components  
FontWeight "com.Hamode.periodpal.ui.components  Home "com.Hamode.periodpal.ui.components  Icon "com.Hamode.periodpal.ui.components  
IconButton "com.Hamode.periodpal.ui.components  Icons "com.Hamode.periodpal.ui.components  ImageVector "com.Hamode.periodpal.ui.components  Int "com.Hamode.periodpal.ui.components  Map "com.Hamode.periodpal.ui.components  
MaterialTheme "com.Hamode.periodpal.ui.components  Modifier "com.Hamode.periodpal.ui.components  NavigationDestination "com.Hamode.periodpal.ui.components  PeriodPalBottomNavigation "com.Hamode.periodpal.ui.components  Person "com.Hamode.periodpal.ui.components  
RosePink40 "com.Hamode.periodpal.ui.components  Row "com.Hamode.periodpal.ui.components  Text "com.Hamode.periodpal.ui.components  Unit "com.Hamode.periodpal.ui.components  align "com.Hamode.periodpal.ui.components  
cardColors "com.Hamode.periodpal.ui.components  
cardElevation "com.Hamode.periodpal.ui.components  emptyMap "com.Hamode.periodpal.ui.components  fillMaxWidth "com.Hamode.periodpal.ui.components  forEach "com.Hamode.periodpal.ui.components  getDestinationIcon "com.Hamode.periodpal.ui.components  padding "com.Hamode.periodpal.ui.components  size "com.Hamode.periodpal.ui.components  weight "com.Hamode.periodpal.ui.components  NavigationDestination "com.Hamode.periodpal.ui.navigation  SecondaryDestination "com.Hamode.periodpal.ui.navigation  String "com.Hamode.periodpal.ui.navigation  CALENDAR 8com.Hamode.periodpal.ui.navigation.NavigationDestination  HEALTH_LOGS 8com.Hamode.periodpal.ui.navigation.NavigationDestination  HOME 8com.Hamode.periodpal.ui.navigation.NavigationDestination  INSIGHTS 8com.Hamode.periodpal.ui.navigation.NavigationDestination  PROFILE 8com.Hamode.periodpal.ui.navigation.NavigationDestination  displayName 8com.Hamode.periodpal.ui.navigation.NavigationDestination  values 8com.Hamode.periodpal.ui.navigation.NavigationDestination  Add com.Hamode.periodpal.ui.screens  	Alignment com.Hamode.periodpal.ui.screens  Arrangement com.Hamode.periodpal.ui.screens  Boolean com.Hamode.periodpal.ui.screens  Box com.Hamode.periodpal.ui.screens  Brush com.Hamode.periodpal.ui.screens  Button com.Hamode.periodpal.ui.screens  ButtonDefaults com.Hamode.periodpal.ui.screens  CalendarDay com.Hamode.periodpal.ui.screens  CalendarGrid com.Hamode.periodpal.ui.screens  CalendarHeader com.Hamode.periodpal.ui.screens  CalendarLegend com.Hamode.periodpal.ui.screens  CalendarScreen com.Hamode.periodpal.ui.screens  
CalendarToday com.Hamode.periodpal.ui.screens  Card com.Hamode.periodpal.ui.screens  CardDefaults com.Hamode.periodpal.ui.screens  ChevronLeft com.Hamode.periodpal.ui.screens  ChevronRight com.Hamode.periodpal.ui.screens  Circle com.Hamode.periodpal.ui.screens  CircleShape com.Hamode.periodpal.ui.screens  Color com.Hamode.periodpal.ui.screens  Column com.Hamode.periodpal.ui.screens  
Composable com.Hamode.periodpal.ui.screens  CycleOverviewCard com.Hamode.periodpal.ui.screens  
CyclePhase com.Hamode.periodpal.ui.screens  DarkGray com.Hamode.periodpal.ui.screens  DateTimeFormatter com.Hamode.periodpal.ui.screens  FertileGreen com.Hamode.periodpal.ui.screens  
FlowIntensity com.Hamode.periodpal.ui.screens  FlowIntensityButton com.Hamode.periodpal.ui.screens  
FontWeight com.Hamode.periodpal.ui.screens  	GridCells com.Hamode.periodpal.ui.screens  
HeaderSection com.Hamode.periodpal.ui.screens  HealthInsightsPreview com.Hamode.periodpal.ui.screens  
HomeScreen com.Hamode.periodpal.ui.screens  Icon com.Hamode.periodpal.ui.screens  
IconButton com.Hamode.periodpal.ui.screens  Icons com.Hamode.periodpal.ui.screens  ImageVector com.Hamode.periodpal.ui.screens  Int com.Hamode.periodpal.ui.screens  LazyVerticalGrid com.Hamode.periodpal.ui.screens  
LegendItem com.Hamode.periodpal.ui.screens  	LightGray com.Hamode.periodpal.ui.screens  	LocalDate com.Hamode.periodpal.ui.screens  
MaterialTheme com.Hamode.periodpal.ui.screens  Modifier com.Hamode.periodpal.ui.screens  
Notifications com.Hamode.periodpal.ui.screens  OverviewItem com.Hamode.periodpal.ui.screens  	PMSPurple com.Hamode.periodpal.ui.screens  PeriodPalThemeColors com.Hamode.periodpal.ui.screens  	PeriodRed com.Hamode.periodpal.ui.screens  	PlayArrow com.Hamode.periodpal.ui.screens  QuickActionButton com.Hamode.periodpal.ui.screens  QuickActionButtons com.Hamode.periodpal.ui.screens  
RosePink40 com.Hamode.periodpal.ui.screens  Row com.Hamode.periodpal.ui.screens  Schedule com.Hamode.periodpal.ui.screens  SelectedDateInfo com.Hamode.periodpal.ui.screens  Settings com.Hamode.periodpal.ui.screens  
SoftPink40 com.Hamode.periodpal.ui.screens  
SoftPink80 com.Hamode.periodpal.ui.screens  Spacer com.Hamode.periodpal.ui.screens  Stop com.Hamode.periodpal.ui.screens  String com.Hamode.periodpal.ui.screens  SuccessGreen com.Hamode.periodpal.ui.screens  Text com.Hamode.periodpal.ui.screens  	TextAlign com.Hamode.periodpal.ui.screens  
TextButton com.Hamode.periodpal.ui.screens  TodayLoggingSection com.Hamode.periodpal.ui.screens  
TrendingUp com.Hamode.periodpal.ui.screens  Unit com.Hamode.periodpal.ui.screens  White com.Hamode.periodpal.ui.screens  	YearMonth com.Hamode.periodpal.ui.screens  androidx com.Hamode.periodpal.ui.screens  
background com.Hamode.periodpal.ui.screens  buttonColors com.Hamode.periodpal.ui.screens  
cardColors com.Hamode.periodpal.ui.screens  
cardElevation com.Hamode.periodpal.ui.screens  clip com.Hamode.periodpal.ui.screens  fillMaxSize com.Hamode.periodpal.ui.screens  fillMaxWidth com.Hamode.periodpal.ui.screens  forEach com.Hamode.periodpal.ui.screens  getFlowColor com.Hamode.periodpal.ui.screens  
getPhaseColor com.Hamode.periodpal.ui.screens  getValue com.Hamode.periodpal.ui.screens  height com.Hamode.periodpal.ui.screens  listOf com.Hamode.periodpal.ui.screens  mutableStateOf com.Hamode.periodpal.ui.screens  padding com.Hamode.periodpal.ui.screens  provideDelegate com.Hamode.periodpal.ui.screens  remember com.Hamode.periodpal.ui.screens  rememberScrollState com.Hamode.periodpal.ui.screens  setValue com.Hamode.periodpal.ui.screens  size com.Hamode.periodpal.ui.screens  spacedBy com.Hamode.periodpal.ui.screens  verticalGradient com.Hamode.periodpal.ui.screens  verticalScroll com.Hamode.periodpal.ui.screens  weight com.Hamode.periodpal.ui.screens  width com.Hamode.periodpal.ui.screens  compose (com.Hamode.periodpal.ui.screens.androidx  ui 0com.Hamode.periodpal.ui.screens.androidx.compose  graphics 3com.Hamode.periodpal.ui.screens.androidx.compose.ui  Color <com.Hamode.periodpal.ui.screens.androidx.compose.ui.graphics  Activity com.Hamode.periodpal.ui.theme  Add com.Hamode.periodpal.ui.theme  	Alignment com.Hamode.periodpal.ui.theme  	Analytics com.Hamode.periodpal.ui.theme  Arrangement com.Hamode.periodpal.ui.theme  
Assignment com.Hamode.periodpal.ui.theme  Badge com.Hamode.periodpal.ui.theme  Black com.Hamode.periodpal.ui.theme  Boolean com.Hamode.periodpal.ui.theme  
BottomNavItem com.Hamode.periodpal.ui.theme  Box com.Hamode.periodpal.ui.theme  Brush com.Hamode.periodpal.ui.theme  Build com.Hamode.periodpal.ui.theme  Button com.Hamode.periodpal.ui.theme  ButtonDefaults com.Hamode.periodpal.ui.theme  CalendarDay com.Hamode.periodpal.ui.theme  CalendarGrid com.Hamode.periodpal.ui.theme  CalendarHeader com.Hamode.periodpal.ui.theme  CalendarLegend com.Hamode.periodpal.ui.theme  
CalendarMonth com.Hamode.periodpal.ui.theme  
CalendarToday com.Hamode.periodpal.ui.theme  Card com.Hamode.periodpal.ui.theme  CardDefaults com.Hamode.periodpal.ui.theme  ChevronLeft com.Hamode.periodpal.ui.theme  ChevronRight com.Hamode.periodpal.ui.theme  Circle com.Hamode.periodpal.ui.theme  CircleShape com.Hamode.periodpal.ui.theme  Color com.Hamode.periodpal.ui.theme  Column com.Hamode.periodpal.ui.theme  
Composable com.Hamode.periodpal.ui.theme  CycleOverviewCard com.Hamode.periodpal.ui.theme  
CyclePhase com.Hamode.periodpal.ui.theme  DarkColorScheme com.Hamode.periodpal.ui.theme  DarkGray com.Hamode.periodpal.ui.theme  DateTimeFormatter com.Hamode.periodpal.ui.theme  ErrorRed com.Hamode.periodpal.ui.theme  FertileGreen com.Hamode.periodpal.ui.theme  	FlowHeavy com.Hamode.periodpal.ui.theme  
FlowIntensity com.Hamode.periodpal.ui.theme  FlowIntensityButton com.Hamode.periodpal.ui.theme  	FlowLight com.Hamode.periodpal.ui.theme  
FlowMedium com.Hamode.periodpal.ui.theme  FlowNone com.Hamode.periodpal.ui.theme  FlowSpotting com.Hamode.periodpal.ui.theme  
FlowVeryHeavy com.Hamode.periodpal.ui.theme  
FontFamily com.Hamode.periodpal.ui.theme  
FontWeight com.Hamode.periodpal.ui.theme  	GridCells com.Hamode.periodpal.ui.theme  
HeaderSection com.Hamode.periodpal.ui.theme  HealthInsightsPreview com.Hamode.periodpal.ui.theme  HighContrastError com.Hamode.periodpal.ui.theme  HighContrastFocus com.Hamode.periodpal.ui.theme  HighContrastSuccess com.Hamode.periodpal.ui.theme  Home com.Hamode.periodpal.ui.theme  Icon com.Hamode.periodpal.ui.theme  
IconButton com.Hamode.periodpal.ui.theme  Icons com.Hamode.periodpal.ui.theme  ImageVector com.Hamode.periodpal.ui.theme  InfoBlue com.Hamode.periodpal.ui.theme  Int com.Hamode.periodpal.ui.theme  LazyVerticalGrid com.Hamode.periodpal.ui.theme  
LegendItem com.Hamode.periodpal.ui.theme  LightColorScheme com.Hamode.periodpal.ui.theme  	LightGray com.Hamode.periodpal.ui.theme  	LocalDate com.Hamode.periodpal.ui.theme  Map com.Hamode.periodpal.ui.theme  
MaterialTheme com.Hamode.periodpal.ui.theme  Modifier com.Hamode.periodpal.ui.theme  MoodAnxious com.Hamode.periodpal.ui.theme  MoodCalm com.Hamode.periodpal.ui.theme  
MoodEmotional com.Hamode.periodpal.ui.theme  
MoodEnergetic com.Hamode.periodpal.ui.theme  	MoodHappy com.Hamode.periodpal.ui.theme  
MoodIrritable com.Hamode.periodpal.ui.theme  MoodSad com.Hamode.periodpal.ui.theme  	MoodTired com.Hamode.periodpal.ui.theme  NavigationDestination com.Hamode.periodpal.ui.theme  
Notifications com.Hamode.periodpal.ui.theme  OverviewItem com.Hamode.periodpal.ui.theme  
OvulationBlue com.Hamode.periodpal.ui.theme  	PMSPurple com.Hamode.periodpal.ui.theme  PainExtreme com.Hamode.periodpal.ui.theme  PainMild com.Hamode.periodpal.ui.theme  PainModerate com.Hamode.periodpal.ui.theme  PainNone com.Hamode.periodpal.ui.theme  
PainSevere com.Hamode.periodpal.ui.theme  PeriodPalTheme com.Hamode.periodpal.ui.theme  PeriodPalThemeColors com.Hamode.periodpal.ui.theme  	PeriodRed com.Hamode.periodpal.ui.theme  Person com.Hamode.periodpal.ui.theme  	PlayArrow com.Hamode.periodpal.ui.theme  QuickActionButton com.Hamode.periodpal.ui.theme  QuickActionButtons com.Hamode.periodpal.ui.theme  
RosePink40 com.Hamode.periodpal.ui.theme  
RosePink80 com.Hamode.periodpal.ui.theme  Row com.Hamode.periodpal.ui.theme  Schedule com.Hamode.periodpal.ui.theme  SelectedDateInfo com.Hamode.periodpal.ui.theme  Settings com.Hamode.periodpal.ui.theme  
SoftPink40 com.Hamode.periodpal.ui.theme  
SoftPink80 com.Hamode.periodpal.ui.theme  Spacer com.Hamode.periodpal.ui.theme  Stop com.Hamode.periodpal.ui.theme  String com.Hamode.periodpal.ui.theme  SuccessGreen com.Hamode.periodpal.ui.theme  Text com.Hamode.periodpal.ui.theme  	TextAlign com.Hamode.periodpal.ui.theme  
TextButton com.Hamode.periodpal.ui.theme  TodayLoggingSection com.Hamode.periodpal.ui.theme  
TrendingUp com.Hamode.periodpal.ui.theme  
Typography com.Hamode.periodpal.ui.theme  Unit com.Hamode.periodpal.ui.theme  
WarningOrange com.Hamode.periodpal.ui.theme  White com.Hamode.periodpal.ui.theme  WindowCompat com.Hamode.periodpal.ui.theme  	YearMonth com.Hamode.periodpal.ui.theme  align com.Hamode.periodpal.ui.theme  androidx com.Hamode.periodpal.ui.theme  
background com.Hamode.periodpal.ui.theme  buttonColors com.Hamode.periodpal.ui.theme  
cardColors com.Hamode.periodpal.ui.theme  
cardElevation com.Hamode.periodpal.ui.theme  clip com.Hamode.periodpal.ui.theme  com com.Hamode.periodpal.ui.theme  emptyMap com.Hamode.periodpal.ui.theme  fillMaxSize com.Hamode.periodpal.ui.theme  fillMaxWidth com.Hamode.periodpal.ui.theme  forEach com.Hamode.periodpal.ui.theme  getFlowColor com.Hamode.periodpal.ui.theme  
getPhaseColor com.Hamode.periodpal.ui.theme  getValue com.Hamode.periodpal.ui.theme  height com.Hamode.periodpal.ui.theme  listOf com.Hamode.periodpal.ui.theme  mutableStateOf com.Hamode.periodpal.ui.theme  padding com.Hamode.periodpal.ui.theme  provideDelegate com.Hamode.periodpal.ui.theme  remember com.Hamode.periodpal.ui.theme  rememberScrollState com.Hamode.periodpal.ui.theme  setValue com.Hamode.periodpal.ui.theme  size com.Hamode.periodpal.ui.theme  spacedBy com.Hamode.periodpal.ui.theme  verticalGradient com.Hamode.periodpal.ui.theme  verticalScroll com.Hamode.periodpal.ui.theme  weight com.Hamode.periodpal.ui.theme  width com.Hamode.periodpal.ui.theme  FertileGreen 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	FlowHeavy 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	FlowLight 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
FlowMedium 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  FlowNone 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  FlowSpotting 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
FlowVeryHeavy 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  MoodAnxious 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  MoodCalm 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
MoodEmotional 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
MoodEnergetic 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	MoodHappy 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
MoodIrritable 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  MoodSad 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	MoodTired 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	PMSPurple 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainExtreme 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainMild 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainModerate 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  PainNone 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
PainSevere 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  	PeriodRed 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
SoftPink40 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  com 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  getFlowColor 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  
getPhaseColor 2com.Hamode.periodpal.ui.theme.PeriodPalThemeColors  compose &com.Hamode.periodpal.ui.theme.androidx  ui .com.Hamode.periodpal.ui.theme.androidx.compose  graphics 1com.Hamode.periodpal.ui.theme.androidx.compose.ui  Color :com.Hamode.periodpal.ui.theme.androidx.compose.ui.graphics  Hamode !com.Hamode.periodpal.ui.theme.com  	periodpal (com.Hamode.periodpal.ui.theme.com.Hamode  data 2com.Hamode.periodpal.ui.theme.com.Hamode.periodpal  models 7com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data  
CyclePhase >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  
FlowIntensity >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  	MoodState >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  	PainLevel >com.Hamode.periodpal.ui.theme.com.Hamode.periodpal.data.models  	LocalDate 	java.time  	YearMonth 	java.time  value java.time.DayOfWeek  
dayOfMonth java.time.LocalDate  	dayOfWeek java.time.LocalDate  format java.time.LocalDate  now java.time.LocalDate  atDay java.time.YearMonth  format java.time.YearMonth  
lengthOfMonth java.time.YearMonth  minusMonths java.time.YearMonth  now java.time.YearMonth  
plusMonths java.time.YearMonth  DateTimeFormatter java.time.format  	ofPattern "java.time.format.DateTimeFormatter  Array kotlin  	Function0 kotlin  	Function1 kotlin  forEach kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  sp 
kotlin.Double  Int kotlin.Enum  String kotlin.Enum  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  toString 
kotlin.Int  List kotlin.collections  Map kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  forEach kotlin.collections  listOf kotlin.collections  get kotlin.collections.Map  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  contains kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  forEach kotlin.sequences  forEach kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              