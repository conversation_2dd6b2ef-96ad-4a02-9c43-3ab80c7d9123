package com.Hamode.periodpal.ui.theme

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.CyclePhase
import com.Hamode.periodpal.data.models.FlowIntensity
import com.Hamode.periodpal.data.models.MoodType
import com.Hamode.periodpal.data.models.PainLevel

@Preview(showBackground = true, name = "PeriodPal Theme Preview")
@Composable
fun ThemePreview() {
    PeriodPalTheme {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Text(
                    text = "PeriodPal Theme Preview",
                    style = MaterialTheme.typography.headlineLarge,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            item {
                ColorPalettePreview()
            }
            
            item {
                TypographyPreview()
            }
            
            item {
                CyclePhaseColorsPreview()
            }
            
            item {
                FlowIntensityColorsPreview()
            }
            
            item {
                ComponentsPreview()
            }
        }
    }
}

@Composable
private fun ColorPalettePreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Color Palette",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // Primary Colors
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ColorSwatch("Rose Pink 80", RosePink80)
                ColorSwatch("Rose Pink 60", RosePink60)
                ColorSwatch("Rose Pink 40", RosePink40)
                ColorSwatch("Rose Pink 20", RosePink20)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Secondary Colors
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ColorSwatch("Soft Pink 80", SoftPink80)
                ColorSwatch("Soft Pink 60", SoftPink60)
                ColorSwatch("Soft Pink 40", SoftPink40)
                ColorSwatch("Soft Pink 20", SoftPink20)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Cycle Phase Colors
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ColorSwatch("Period", PeriodRed)
                ColorSwatch("Fertile", FertileGreen)
                ColorSwatch("PMS", PMSPurple)
                ColorSwatch("Ovulation", OvulationBlue)
            }
        }
    }
}

@Composable
private fun ColorSwatch(name: String, color: Color) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.weight(1f)
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(color)
        )
        Text(
            text = name,
            style = MaterialTheme.typography.labelSmall,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

@Composable
private fun TypographyPreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Typography",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Text("Display Large", style = MaterialTheme.typography.displayLarge)
            Text("Headline Medium", style = MaterialTheme.typography.headlineMedium)
            Text("Title Large", style = MaterialTheme.typography.titleLarge)
            Text("Body Large - This is the main body text style", style = MaterialTheme.typography.bodyLarge)
            Text("Body Medium - Secondary body text", style = MaterialTheme.typography.bodyMedium)
            Text("Label Large", style = MaterialTheme.typography.labelLarge)
        }
    }
}

@Composable
private fun CyclePhaseColorsPreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Cycle Phase Colors",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            CyclePhase.values().forEach { phase ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(PeriodPalThemeColors.getPhaseColor(phase))
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = phase.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun FlowIntensityColorsPreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Flow Intensity Colors",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            FlowIntensity.values().forEach { intensity ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(PeriodPalThemeColors.getFlowColor(intensity))
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = intensity.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun ComponentsPreview() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "UI Components",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // Buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Primary Button")
                }
                
                OutlinedButton(
                    onClick = { },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Outlined")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Cards with different elevations
            Text(
                text = "Card Elevations",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                repeat(3) { index ->
                    Card(
                        modifier = Modifier
                            .weight(1f)
                            .height(60.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = (index + 1) * 4.dp)
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text("Level ${index + 1}")
                        }
                    }
                }
            }
        }
    }
}
