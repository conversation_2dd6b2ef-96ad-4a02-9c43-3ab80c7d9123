package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.CyclePhase
import com.Hamode.periodpal.data.models.FlowIntensity
import com.Hamode.periodpal.ui.components.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    modifier: Modifier = Modifier,
    onNavigateToCalendar: () -> Unit = {},
    onNavigateToLogs: () -> Unit = {},
    onNavigateToInsights: () -> Unit = {},
    onNavigateToSettings: () -> Unit = {}
) {
    var currentDate by remember { mutableStateOf(LocalDate.now()) }
    var currentCycleDay by remember { mutableStateOf(15) }
    var currentPhase by remember { mutableStateOf(CyclePhase.FOLLICULAR) }
    var flowIntensity by remember { mutableStateOf(FlowIntensity.NONE) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        SoftPink80.copy(alpha = 0.1f)
                    )
                )
            )
    ) {
        // Header Section
        HeaderSection(
            currentDate = currentDate,
            cycleDay = currentCycleDay,
            phase = currentPhase,
            onNotificationClick = { /* Handle notifications */ },
            onSettingsClick = onNavigateToSettings,
            modifier = Modifier.fillMaxWidth()
        )
        
        // Main Content - Scrollable
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Enhanced Calendar Widget
            CalendarViewPager(
                currentDate = currentDate,
                cycleDay = currentCycleDay,
                phase = currentPhase,
                onDateSelected = { date ->
                    currentDate = date
                    // Update cycle day based on selected date
                    // This would normally come from your data layer
                },
                cycleStartDate = currentDate.minusDays(currentCycleDay.toLong() - 1),
                fertileWindow = Pair(
                    currentDate.minusDays(2),
                    currentDate.plusDays(2)
                ),
                pmsWindow = Pair(
                    currentDate.plusDays(10),
                    currentDate.plusDays(16)
                ),
                periodDays = listOf(
                    currentDate.minusDays(currentCycleDay.toLong() - 1),
                    currentDate.minusDays(currentCycleDay.toLong() - 2),
                    currentDate.minusDays(currentCycleDay.toLong() - 3)
                ),
                nextPeriodPrediction = currentDate.plusDays(14),
                calendarData = mapOf(
                    currentDate to CalendarDayData(
                        date = currentDate,
                        isToday = true,
                        cycleDay = currentCycleDay,
                        phase = currentPhase,
                        isFertile = currentPhase == CyclePhase.OVULATION
                    )
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            )
            
            // Quick Action Buttons
            QuickActionButtons(
                onStartPeriod = { 
                    flowIntensity = FlowIntensity.LIGHT
                    currentPhase = CyclePhase.MENSTRUAL
                },
                onEndPeriod = { 
                    flowIntensity = FlowIntensity.NONE
                },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Daily Logging Section
            DailyLoggingSection(
                selectedDate = currentDate,
                flowIntensity = flowIntensity,
                onFlowIntensityChanged = { flowIntensity = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Health Insights Preview
            HealthInsightsCard(
                onViewAllInsights = onNavigateToInsights,
                modifier = Modifier.fillMaxWidth()
            )
            
            // Bottom spacing for navigation
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun QuickActionButtons(
    onStartPeriod: () -> Unit,
    onEndPeriod: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick Actions",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Button(
                    onClick = onStartPeriod,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = PeriodRed
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "Start Period",
                        color = White,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                OutlinedButton(
                    onClick = onEndPeriod,
                    modifier = Modifier.weight(1f),
                    border = ButtonDefaults.outlinedButtonBorder.copy(
                        brush = Brush.horizontalGradient(
                            colors = listOf(RosePink40, SoftPink40)
                        )
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "End Period",
                        color = RosePink40,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, name = "Main Screen Preview")
@Composable
fun MainScreenPreview() {
    PeriodPalTheme {
        MainScreen()
    }
}
