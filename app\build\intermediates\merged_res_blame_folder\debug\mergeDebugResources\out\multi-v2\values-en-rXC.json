{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-45:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8604,8794,8984,9189,9371,9557,9761,9965,10167,10369,10559,10768,10972,11179,11398,11581,11782", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,206,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8599,8789,8979,9184,9366,9552,9756,9960,10162,10364,10554,10763,10967,11174,11393,11576,11777,11975"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2872,3092,3309,3523,3740,3941,4139,4349,4588,4806,5045,5231,5431,5625,5825,6046,6274,6481,6711,6937,7166,7427,7649,7868,8089,8314,8509,8709,8927,9153,9352,9555,9760,9990,10231,10440,10641,10820,11018,11214,11421,11611,11801,12006,12188,12374,12578,12782,12984,13186,13376,13585,13789,13996,14215,14398,14599", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,206,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "3087,3304,3518,3735,3936,4134,4344,4583,4801,5040,5226,5426,5620,5820,6041,6269,6476,6706,6932,7161,7422,7644,7863,8084,8309,8504,8704,8922,9148,9347,9550,9755,9985,10226,10435,10636,10815,11013,11209,11416,11606,11796,12001,12183,12369,12573,12777,12979,13181,13371,13580,13784,13991,14210,14393,14594,14792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,301,506,707,908,1115,1320,16228", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "296,501,702,903,1110,1315,1527,16427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,2001,2186,2352,2530,2705,2876,3055,3222", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1996,2181,2347,2525,2700,2871,3050,3217,3455"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1532,1723,1908,2105,2307,2494,2679,14797,14985,15172,15353,15538,15704,15882,16057,16432,16611,16778", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "1718,1903,2100,2302,2489,2674,2867,14980,15167,15348,15533,15699,15877,16052,16223,16606,16773,17011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "17016,17204", "endColumns": "187,186", "endOffsets": "17199,17386"}}]}]}