package com.Hamode.periodpal.data.models

/**
 * Represents different types of symptoms that can be tracked
 */
enum class SymptomType(val displayName: String, val category: String, val icon: String) {
    // Physical Symptoms
    CRAMPS("Cramps", "Physical", "🤕"),
    BLOATING("Bloating", "Physical", "🎈"),
    HEADACHE("Headache", "Physical", "🤯"),
    BREAST_TENDERNESS("Breast Tenderness", "Physical", "💔"),
    BACK_PAIN("Back Pain", "Physical", "🦴"),
    NAUSEA("Nausea", "Physical", "🤢"),
    FATIGUE("Fatigue", "Physical", "😴"),
    DIZZINESS("Dizziness", "Physical", "💫"),
    HOT_FLASHES("Hot Flashes", "Physical", "🔥"),
    JOINT_PAIN("Joint Pain", "Physical", "🦴"),
    
    // Skin & Hair
    ACNE("Acne", "Skin", "🔴"),
    OILY_SKIN("Oily Skin", "Skin", "✨"),
    DRY_SKIN("Dry Skin", "Skin", "🏜️"),
    HAIR_CHANGES("Hair Changes", "Hair", "💇‍♀️"),
    
    // Digestive
    CONSTIPATION("Constipation", "Digestive", "🚫"),
    DIARRHEA("Diarrhea", "Digestive", "💨"),
    FOOD_CRAVINGS("Food Cravings", "Digestive", "🍫"),
    APPETITE_CHANGES("Appetite Changes", "Digestive", "🍽️"),
    
    // Sleep & Energy
    INSOMNIA("Insomnia", "Sleep", "🌙"),
    VIVID_DREAMS("Vivid Dreams", "Sleep", "💭"),
    ENERGY_BOOST("Energy Boost", "Energy", "⚡"),
    LOW_ENERGY("Low Energy", "Energy", "🔋"),
    
    // Other
    INCREASED_LIBIDO("Increased Libido", "Other", "💕"),
    DECREASED_LIBIDO("Decreased Libido", "Other", "💔"),
    CERVICAL_MUCUS_CHANGES("Cervical Mucus Changes", "Other", "💧");
    
    companion object {
        fun getByCategory(category: String): List<SymptomType> {
            return values().filter { it.category == category }
        }
        
        fun getAllCategories(): List<String> {
            return values().map { it.category }.distinct().sorted()
        }
    }
}
