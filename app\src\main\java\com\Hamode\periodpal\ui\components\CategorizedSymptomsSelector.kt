package com.Hamode.periodpal.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.SymptomType
import com.Hamode.periodpal.ui.theme.*

@Composable
fun CategorizedSymptomsSelector(
    selectedSymptoms: Set<SymptomType>,
    onSymptomsChanged: (Set<SymptomType>) -> Unit,
    modifier: Modifier = Modifier
) {
    var expandedCategories by remember { mutableStateOf(setOf<String>()) }
    val categories = SymptomType.getAllCategories()
    
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Symptoms",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold
                )
                
                if (selectedSymptoms.isNotEmpty()) {
                    Text(
                        text = "${selectedSymptoms.size} selected",
                        style = MaterialTheme.typography.bodySmall,
                        color = RosePink40
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Quick Common Symptoms
            QuickSymptomsRow(
                selectedSymptoms = selectedSymptoms,
                onSymptomsChanged = onSymptomsChanged,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Categorized Symptoms
            categories.forEach { category ->
                SymptomCategorySection(
                    category = category,
                    symptoms = SymptomType.getByCategory(category),
                    selectedSymptoms = selectedSymptoms,
                    onSymptomsChanged = onSymptomsChanged,
                    isExpanded = expandedCategories.contains(category),
                    onExpandedChanged = { isExpanded ->
                        expandedCategories = if (isExpanded) {
                            expandedCategories + category
                        } else {
                            expandedCategories - category
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
                
                if (category != categories.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

@Composable
private fun QuickSymptomsRow(
    selectedSymptoms: Set<SymptomType>,
    onSymptomsChanged: (Set<SymptomType>) -> Unit,
    modifier: Modifier = Modifier
) {
    val commonSymptoms = listOf(
        SymptomType.CRAMPS,
        SymptomType.BLOATING,
        SymptomType.HEADACHE,
        SymptomType.BREAST_TENDERNESS,
        SymptomType.FATIGUE,
        SymptomType.NAUSEA
    )
    
    Column(modifier = modifier) {
        Text(
            text = "Quick Select",
            style = MaterialTheme.typography.labelMedium,
            color = DarkGray.copy(alpha = 0.7f),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(commonSymptoms) { symptom ->
                QuickSymptomChip(
                    symptom = symptom,
                    isSelected = selectedSymptoms.contains(symptom),
                    onClick = {
                        val newSymptoms = if (selectedSymptoms.contains(symptom)) {
                            selectedSymptoms - symptom
                        } else {
                            selectedSymptoms + symptom
                        }
                        onSymptomsChanged(newSymptoms)
                    }
                )
            }
        }
    }
}

@Composable
private fun QuickSymptomChip(
    symptom: SymptomType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            RosePink40.copy(alpha = 0.2f)
        } else {
            LightGray.copy(alpha = 0.3f)
        },
        label = "backgroundColor"
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (isSelected) {
            RosePink40
        } else {
            Color.Transparent
        },
        label = "borderColor"
    )
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(backgroundColor)
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = borderColor,
                shape = RoundedCornerShape(20.dp)
            )
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Text(
            text = symptom.icon,
            fontSize = 16.sp,
            modifier = Modifier.padding(end = 6.dp)
        )
        Text(
            text = symptom.displayName,
            style = MaterialTheme.typography.labelMedium,
            color = if (isSelected) RosePink40 else DarkGray,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

@Composable
private fun SymptomCategorySection(
    category: String,
    symptoms: List<SymptomType>,
    selectedSymptoms: Set<SymptomType>,
    onSymptomsChanged: (Set<SymptomType>) -> Unit,
    isExpanded: Boolean,
    onExpandedChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val selectedInCategory = symptoms.count { selectedSymptoms.contains(it) }
    val rotationAngle by animateFloatAsState(
        targetValue = if (isExpanded) 180f else 0f,
        label = "rotationAngle"
    )
    
    Column(modifier = modifier) {
        // Category Header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(8.dp))
                .clickable { onExpandedChanged(!isExpanded) }
                .padding(vertical = 8.dp, horizontal = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = category,
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.Medium
                )
                
                if (selectedInCategory > 0) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Box(
                        modifier = Modifier
                            .background(
                                color = RosePink40,
                                shape = RoundedCornerShape(10.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    ) {
                        Text(
                            text = selectedInCategory.toString(),
                            style = MaterialTheme.typography.labelSmall,
                            color = White,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
            
            Icon(
                imageVector = Icons.Default.ExpandMore,
                contentDescription = if (isExpanded) "Collapse" else "Expand",
                tint = DarkGray.copy(alpha = 0.7f),
                modifier = Modifier
                    .size(20.dp)
                    .rotate(rotationAngle)
            )
        }
        
        // Category Symptoms
        if (isExpanded) {
            Spacer(modifier = Modifier.height(8.dp))
            
            symptoms.chunked(2).forEach { rowSymptoms ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    rowSymptoms.forEach { symptom ->
                        SymptomItem(
                            symptom = symptom,
                            isSelected = selectedSymptoms.contains(symptom),
                            onClick = {
                                val newSymptoms = if (selectedSymptoms.contains(symptom)) {
                                    selectedSymptoms - symptom
                                } else {
                                    selectedSymptoms + symptom
                                }
                                onSymptomsChanged(newSymptoms)
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    
                    // Fill remaining space if odd number of symptoms
                    if (rowSymptoms.size == 1) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
                
                if (rowSymptoms != symptoms.chunked(2).last()) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
private fun SymptomItem(
    symptom: SymptomType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.02f else 1f,
        label = "scale"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            RosePink40.copy(alpha = 0.1f)
        } else {
            LightGray.copy(alpha = 0.2f)
        },
        label = "backgroundColor"
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (isSelected) {
            RosePink40
        } else {
            Color.Transparent
        },
        label = "borderColor"
    )
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .scale(scale)
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            )
            .clickable { onClick() }
            .padding(12.dp)
    ) {
        Text(
            text = symptom.icon,
            fontSize = 18.sp,
            modifier = Modifier.padding(end = 8.dp)
        )
        
        Text(
            text = symptom.displayName,
            style = MaterialTheme.typography.bodyMedium,
            color = if (isSelected) RosePink40 else MaterialTheme.colorScheme.onSurface,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            modifier = Modifier.weight(1f)
        )
    }
}

@Preview(showBackground = true, name = "Categorized Symptoms Selector Preview")
@Composable
fun CategorizedSymptomsSelectorPreview() {
    PeriodPalTheme {
        CategorizedSymptomsSelector(
            selectedSymptoms = setOf(SymptomType.CRAMPS, SymptomType.HEADACHE),
            onSymptomsChanged = { }
        )
    }
}
