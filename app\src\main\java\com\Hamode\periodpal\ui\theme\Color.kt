package com.Hamode.periodpal.ui.theme

import androidx.compose.ui.graphics.Color

// Primary Rose Pink Colors
val RosePink80 = Color(0xFFF8BBD9)
val RosePink60 = Color(0xFFF48FB1)
val RosePink40 = Color(0xFFE91E63)
val RosePink20 = Color(0xFFAD1457)

// Secondary Soft Pink Colors
val SoftPink80 = Color(0xFFFCE4EC)
val SoftPink60 = Color(0xFFF8BBD9)
val SoftPink40 = Color(0xFFF48FB1)
val SoftPink20 = Color(0xFFE91E63)

// Neutral Colors
val White = Color(0xFFFFFFFF)
val OffWhite = Color(0xFFFFFBFE)
val LightGray = Color(0xFFF5F5F5)
val MediumGray = Color(0xFFE0E0E0)
val DarkGray = Color(0xFF424242)
val Black = Color(0xFF000000)

// Accent Colors for Different Cycle Phases
val FertileGreen = Color(0xFF4CAF50)
val PMSPurple = Color(0xFF9C27B0)
val PeriodRed = Color(0xFFE53935)
val OvulationBlue = Color(0xFF2196F3)

// Status Colors
val SuccessGreen = Color(0xFF4CAF50)
val WarningOrange = Color(0xFFFF9800)
val ErrorRed = Color(0xFFF44336)
val InfoBlue = Color(0xFF2196F3)