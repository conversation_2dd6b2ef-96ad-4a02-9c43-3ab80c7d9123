package com.Hamode.periodpal.ui.accessibility

import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.ui.theme.*

/**
 * Accessibility preferences and settings
 */
data class AccessibilitySettings(
    val isHighContrastEnabled: Boolean = false,
    val isLargeTextEnabled: Boolean = false,
    val isReduceMotionEnabled: Boolean = false,
    val isScreenReaderEnabled: Boolean = false,
    val textScaleFactor: Float = 1.0f,
    val minimumTouchTargetSize: Dp = 44.dp,
    val isColorBlindFriendlyEnabled: Boolean = false,
    val isFocusIndicatorEnabled: Boolean = true
)

/**
 * Accessibility manager for the PeriodPal app
 */
@Composable
fun rememberAccessibilitySettings(): AccessibilitySettings {
    val context = LocalContext.current
    
    // In a real app, these would come from system settings and user preferences
    return remember {
        AccessibilitySettings(
            isHighContrastEnabled = false, // Would check system settings
            isLargeTextEnabled = false, // Would check system settings
            isReduceMotionEnabled = false, // Would check system settings
            isScreenReaderEnabled = false, // Would check if TalkBack is enabled
            textScaleFactor = 1.0f, // Would get from system font scale
            isColorBlindFriendlyEnabled = false, // User preference
            isFocusIndicatorEnabled = true // Always enabled for accessibility
        )
    }
}

/**
 * Accessibility utilities for consistent implementation
 */
object AccessibilityUtils {
    
    /**
     * Gets accessible text size based on user preferences
     */
    @Composable
    fun getAccessibleTextSize(
        baseSize: TextUnit,
        settings: AccessibilitySettings = rememberAccessibilitySettings()
    ): TextUnit {
        return baseSize * settings.textScaleFactor
    }
    
    /**
     * Gets accessible color with proper contrast
     */
    @Composable
    fun getAccessibleColor(
        color: Color,
        backgroundColor: Color = MaterialTheme.colorScheme.surface,
        settings: AccessibilitySettings = rememberAccessibilitySettings()
    ): Color {
        return if (settings.isHighContrastEnabled) {
            AccessibilityColors.getHighContrastColor(color)
        } else {
            color
        }
    }
    
    /**
     * Gets minimum touch target size for accessibility
     */
    @Composable
    fun getMinimumTouchTargetSize(
        settings: AccessibilitySettings = rememberAccessibilitySettings()
    ): Dp {
        return settings.minimumTouchTargetSize
    }
    
    /**
     * Creates accessible text style
     */
    @Composable
    fun getAccessibleTextStyle(
        baseStyle: TextStyle,
        settings: AccessibilitySettings = rememberAccessibilitySettings()
    ): TextStyle {
        return baseStyle.copy(
            fontSize = getAccessibleTextSize(baseStyle.fontSize, settings),
            fontWeight = if (settings.isHighContrastEnabled) FontWeight.SemiBold else baseStyle.fontWeight
        )
    }
    
    /**
     * Gets animation duration based on reduce motion preference
     */
    @Composable
    fun getAnimationDuration(
        normalDuration: Int,
        settings: AccessibilitySettings = rememberAccessibilitySettings()
    ): Int {
        return if (settings.isReduceMotionEnabled) 0 else normalDuration
    }
}

/**
 * Semantic descriptions for screen readers
 */
object SemanticDescriptions {
    
    // Cycle phase descriptions
    fun getCyclePhaseDescription(phase: com.Hamode.periodpal.data.models.CyclePhase): String {
        return when (phase) {
            com.Hamode.periodpal.data.models.CyclePhase.MENSTRUAL -> 
                "Menstrual phase: This is your period, typically lasting 3-7 days"
            com.Hamode.periodpal.data.models.CyclePhase.FOLLICULAR -> 
                "Follicular phase: The phase after your period when your body prepares for ovulation"
            com.Hamode.periodpal.data.models.CyclePhase.OVULATION -> 
                "Ovulation phase: Your most fertile time when an egg is released"
            com.Hamode.periodpal.data.models.CyclePhase.LUTEAL -> 
                "Luteal phase: The phase after ovulation, may include PMS symptoms"
        }
    }
    
    // Flow intensity descriptions
    fun getFlowIntensityDescription(intensity: com.Hamode.periodpal.data.models.FlowIntensity): String {
        return when (intensity) {
            com.Hamode.periodpal.data.models.FlowIntensity.NONE -> "No menstrual flow"
            com.Hamode.periodpal.data.models.FlowIntensity.SPOTTING -> "Very light spotting, minimal flow"
            com.Hamode.periodpal.data.models.FlowIntensity.LIGHT -> "Light flow, may need to change protection every 4-6 hours"
            com.Hamode.periodpal.data.models.FlowIntensity.MEDIUM -> "Medium flow, may need to change protection every 3-4 hours"
            com.Hamode.periodpal.data.models.FlowIntensity.HEAVY -> "Heavy flow, may need to change protection every 2-3 hours"
            com.Hamode.periodpal.data.models.FlowIntensity.VERY_HEAVY -> "Very heavy flow, may need to change protection every 1-2 hours"
        }
    }
    
    // Pain level descriptions
    fun getPainLevelDescription(painLevel: com.Hamode.periodpal.data.models.PainLevel): String {
        return when (painLevel) {
            com.Hamode.periodpal.data.models.PainLevel.NONE -> "No pain or discomfort"
            com.Hamode.periodpal.data.models.PainLevel.MILD -> "Mild pain, barely noticeable, doesn't interfere with activities"
            com.Hamode.periodpal.data.models.PainLevel.MODERATE -> "Moderate pain, noticeable but manageable with daily activities"
            com.Hamode.periodpal.data.models.PainLevel.SEVERE -> "Severe pain, significantly interferes with daily activities"
            com.Hamode.periodpal.data.models.PainLevel.EXTREME -> "Extreme pain, unable to perform normal activities"
        }
    }
    
    // Mood descriptions
    fun getMoodDescription(mood: com.Hamode.periodpal.data.models.MoodType): String {
        return when (mood) {
            com.Hamode.periodpal.data.models.MoodType.HAPPY -> "Happy mood: Feeling joyful and positive"
            com.Hamode.periodpal.data.models.MoodType.SAD -> "Sad mood: Feeling down or melancholy"
            com.Hamode.periodpal.data.models.MoodType.ANXIOUS -> "Anxious mood: Feeling worried or nervous"
            com.Hamode.periodpal.data.models.MoodType.IRRITABLE -> "Irritable mood: Feeling easily annoyed or frustrated"
            com.Hamode.periodpal.data.models.MoodType.CALM -> "Calm mood: Feeling peaceful and relaxed"
            com.Hamode.periodpal.data.models.MoodType.ENERGETIC -> "Energetic mood: Feeling full of energy and vitality"
            com.Hamode.periodpal.data.models.MoodType.TIRED -> "Tired mood: Feeling fatigued or low energy"
            com.Hamode.periodpal.data.models.MoodType.EMOTIONAL -> "Emotional mood: Feeling sensitive or tearful"
            com.Hamode.periodpal.data.models.MoodType.CONFIDENT -> "Confident mood: Feeling self-assured and positive"
            com.Hamode.periodpal.data.models.MoodType.STRESSED -> "Stressed mood: Feeling overwhelmed or under pressure"
        }
    }
    
    // Navigation descriptions
    fun getNavigationDescription(destination: String, isSelected: Boolean): String {
        val status = if (isSelected) "currently selected" else "not selected"
        return "$destination tab, $status. Double tap to navigate."
    }
    
    // Calendar descriptions
    fun getCalendarDayDescription(
        date: java.time.LocalDate,
        cycleDay: Int?,
        phase: com.Hamode.periodpal.data.models.CyclePhase?,
        hasSymptoms: Boolean,
        flowIntensity: com.Hamode.periodpal.data.models.FlowIntensity
    ): String {
        val dateStr = date.format(java.time.format.DateTimeFormatter.ofPattern("EEEE, MMMM dd, yyyy"))
        val cycleDayStr = cycleDay?.let { "Cycle day $it" } ?: ""
        val phaseStr = phase?.let { getCyclePhaseDescription(it) } ?: ""
        val flowStr = if (flowIntensity != com.Hamode.periodpal.data.models.FlowIntensity.NONE) {
            getFlowIntensityDescription(flowIntensity)
        } else ""
        val symptomsStr = if (hasSymptoms) "Has logged symptoms" else ""
        
        return listOf(dateStr, cycleDayStr, phaseStr, flowStr, symptomsStr)
            .filter { it.isNotEmpty() }
            .joinToString(". ")
    }
    
    // Button action descriptions
    fun getButtonActionDescription(action: String, context: String = ""): String {
        return if (context.isNotEmpty()) {
            "$action button for $context. Double tap to activate."
        } else {
            "$action button. Double tap to activate."
        }
    }
}

/**
 * Accessibility modifier extensions
 */
fun Modifier.accessibleClickable(
    label: String,
    action: String = "activate",
    onClick: () -> Unit
): Modifier = this.semantics {
    contentDescription = label
    role = Role.Button
    onClick(label = action) {
        onClick()
        true
    }
}

fun Modifier.accessibleSelectable(
    label: String,
    isSelected: Boolean,
    onSelectionChanged: (Boolean) -> Unit
): Modifier = this.semantics {
    contentDescription = label
    role = Role.RadioButton
    selected = isSelected
    onClick(label = if (isSelected) "deselect" else "select") {
        onSelectionChanged(!isSelected)
        true
    }
}

fun Modifier.accessibleSlider(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit
): Modifier = this.semantics {
    contentDescription = label
    role = Role.Slider
    setProgress(label = "adjust") { targetValue ->
        val newValue = targetValue.coerceIn(valueRange)
        onValueChange(newValue)
        true
    }
}

fun Modifier.accessibleHeading(level: Int = 1): Modifier = this.semantics {
    heading()
}

fun Modifier.accessibleLiveRegion(politeness: LiveRegionMode = LiveRegionMode.Polite): Modifier = this.semantics {
    liveRegion = politeness
}
