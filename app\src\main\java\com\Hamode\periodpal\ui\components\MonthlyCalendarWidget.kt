package com.Hamode.periodpal.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.semantics.*
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.foundation.focusable
import androidx.compose.foundation.border
import com.Hamode.periodpal.data.models.CyclePhase
import com.Hamode.periodpal.data.models.FlowIntensity
import com.Hamode.periodpal.ui.accessibility.*
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.data.utils.DateUtils
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter

data class CalendarDayData(
    val date: LocalDate,
    val isCurrentMonth: Boolean = true,
    val isToday: Boolean = false,
    val cycleDay: Int? = null,
    val phase: CyclePhase? = null,
    val flowIntensity: FlowIntensity = FlowIntensity.NONE,
    val isFertile: Boolean = false,
    val isPMS: Boolean = false,
    val isPredictedPeriod: Boolean = false,
    val hasSymptoms: Boolean = false
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonthlyCalendarWidget(
    selectedMonth: YearMonth,
    onMonthChanged: (YearMonth) -> Unit,
    onDateSelected: (LocalDate) -> Unit,
    calendarData: Map<LocalDate, CalendarDayData>,
    modifier: Modifier = Modifier
) {
    var currentMonth by remember { mutableStateOf(selectedMonth) }
    
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Month Header
            MonthHeader(
                currentMonth = currentMonth,
                onPreviousMonth = { 
                    currentMonth = currentMonth.minusMonths(1)
                    onMonthChanged(currentMonth)
                },
                onNextMonth = { 
                    currentMonth = currentMonth.plusMonths(1)
                    onMonthChanged(currentMonth)
                },
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Days of Week Header
            DaysOfWeekHeader(
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Calendar Grid
            CalendarGrid(
                currentMonth = currentMonth,
                calendarData = calendarData,
                onDateSelected = onDateSelected,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Calendar Legend
            CalendarLegend(
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun MonthHeader(
    currentMonth: YearMonth,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onPreviousMonth,
            modifier = Modifier
                .size(40.dp)
                .background(
                    color = RosePink40.copy(alpha = 0.1f),
                    shape = CircleShape
                )
        ) {
            Icon(
                imageVector = Icons.Default.ChevronLeft,
                contentDescription = "Previous month",
                tint = RosePink40
            )
        }
        
        Text(
            text = currentMonth.format(DateTimeFormatter.ofPattern("MMMM yyyy")),
            style = MaterialTheme.typography.titleLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.SemiBold
        )
        
        IconButton(
            onClick = onNextMonth,
            modifier = Modifier
                .size(40.dp)
                .background(
                    color = RosePink40.copy(alpha = 0.1f),
                    shape = CircleShape
                )
        ) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Next month",
                tint = RosePink40
            )
        }
    }
}

@Composable
private fun DaysOfWeekHeader(
    modifier: Modifier = Modifier
) {
    val daysOfWeek = listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat")
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        daysOfWeek.forEach { day ->
            Text(
                text = day,
                style = MaterialTheme.typography.labelMedium,
                color = DarkGray.copy(alpha = 0.7f),
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun CalendarGrid(
    currentMonth: YearMonth,
    calendarData: Map<LocalDate, CalendarDayData>,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    val weeks = DateUtils.getCalendarWeeksForMonth(currentMonth.atDay(1))
    
    Column(modifier = modifier) {
        weeks.forEach { week ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                week.forEach { date ->
                    if (date != null) {
                        val dayData = calendarData[date] ?: CalendarDayData(
                            date = date,
                            isCurrentMonth = date.month == currentMonth.month
                        )
                        
                        CalendarDayCell(
                            dayData = dayData,
                            onClick = { onDateSelected(date) },
                            modifier = Modifier.weight(1f)
                        )
                    } else {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

@Composable
private fun CalendarDayCell(
    dayData: CalendarDayData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    var isFocused by remember { mutableStateOf(false) }
    val backgroundColor = when {
        dayData.isToday -> RosePink40
        dayData.flowIntensity != FlowIntensity.NONE -> PeriodRed.copy(alpha = 0.3f)
        dayData.isFertile -> FertileGreen.copy(alpha = 0.2f)
        dayData.isPMS -> PMSPurple.copy(alpha = 0.1f)
        dayData.isPredictedPeriod -> PeriodRed.copy(alpha = 0.1f)
        else -> Color.Transparent
    }
    
    val textColor = when {
        dayData.isToday -> White
        !dayData.isCurrentMonth -> DarkGray.copy(alpha = 0.3f)
        dayData.flowIntensity != FlowIntensity.NONE -> PeriodRed
        dayData.isFertile -> FertileGreen
        dayData.isPMS -> PMSPurple
        else -> MaterialTheme.colorScheme.onSurface
    }
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .defaultMinSize(
                minWidth = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings),
                minHeight = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings)
            )
            .clip(CircleShape)
            .background(backgroundColor)
            .border(
                width = if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) 2.dp else 0.dp,
                color = AccessibilityColors.getFocusIndicatorColor(),
                shape = CircleShape
            )
            .clickable { onClick() }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .semantics {
                contentDescription = SemanticDescriptions.getCalendarDayDescription(
                    date = dayData.date,
                    cycleDay = dayData.cycleDay,
                    phase = dayData.phase,
                    hasSymptoms = dayData.hasSymptoms,
                    flowIntensity = dayData.flowIntensity
                )
                role = Role.Button
                if (dayData.isToday) {
                    stateDescription = "Today"
                }
            },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = dayData.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodyMedium,
                color = textColor,
                fontWeight = if (dayData.isToday) FontWeight.Bold else FontWeight.Normal
            )
            
            // Indicators
            Row(
                horizontalArrangement = Arrangement.spacedBy(1.dp)
            ) {
                if (dayData.flowIntensity != FlowIntensity.NONE) {
                    Box(
                        modifier = Modifier
                            .size(4.dp)
                            .clip(CircleShape)
                            .background(PeriodRed)
                    )
                }
                
                if (dayData.hasSymptoms) {
                    Box(
                        modifier = Modifier
                            .size(4.dp)
                            .clip(CircleShape)
                            .background(WarningOrange)
                    )
                }
                
                if (dayData.isPredictedPeriod) {
                    Box(
                        modifier = Modifier
                            .size(4.dp)
                            .clip(CircleShape)
                            .background(PeriodRed.copy(alpha = 0.5f))
                    )
                }
            }
        }
    }
}

@Composable
private fun CalendarLegend(
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "Legend",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            LegendItem("Period", PeriodRed, modifier = Modifier.weight(1f))
            LegendItem("Fertile", FertileGreen, modifier = Modifier.weight(1f))
            LegendItem("PMS", PMSPurple, modifier = Modifier.weight(1f))
            LegendItem("Symptoms", WarningOrange, modifier = Modifier.weight(1f))
        }
    }
}

@Composable
private fun LegendItem(
    label: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(color)
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray
        )
    }
}

@Preview(showBackground = true, name = "Monthly Calendar Widget Preview")
@Composable
fun MonthlyCalendarWidgetPreview() {
    val today = LocalDate.now()
    val currentMonth = YearMonth.from(today)
    
    val sampleData = mapOf(
        today to CalendarDayData(
            date = today,
            isToday = true,
            cycleDay = 15,
            phase = CyclePhase.OVULATION,
            isFertile = true
        ),
        today.minusDays(10) to CalendarDayData(
            date = today.minusDays(10),
            flowIntensity = FlowIntensity.MEDIUM,
            cycleDay = 5
        ),
        today.plusDays(5) to CalendarDayData(
            date = today.plusDays(5),
            isPMS = true,
            hasSymptoms = true
        )
    )
    
    PeriodPalTheme {
        MonthlyCalendarWidget(
            selectedMonth = currentMonth,
            onMonthChanged = { },
            onDateSelected = { },
            calendarData = sampleData
        )
    }
}
