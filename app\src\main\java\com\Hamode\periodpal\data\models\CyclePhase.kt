package com.Hamode.periodpal.data.models

/**
 * Represents different phases of the menstrual cycle
 */
enum class CyclePhase(val displayName: String, val description: String) {
    MENSTRUAL("Menstrual", "Period days - menstrual flow"),
    FOLLICULAR("Follicular", "Post-period phase - preparing for ovulation"),
    OVULATION("Ovulation", "Fertile window - highest chance of conception"),
    LUTEAL("Luteal", "Post-ovulation phase - may include PMS symptoms");
    
    companion object {
        /**
         * Determines cycle phase based on cycle day and cycle length
         */
        fun fromCycleDay(cycleDay: Int, cycleLength: Int): CyclePhase {
            return when {
                cycleDay <= 5 -> MENSTRUAL
                cycleDay <= cycleLength / 2 - 2 -> FOLLICULAR
                cycleDay <= cycleLength / 2 + 2 -> OVULATION
                else -> LUTEAL
            }
        }
    }
}
