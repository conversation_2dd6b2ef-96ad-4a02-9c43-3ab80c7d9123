package com.Hamode.periodpal.data.models

/**
 * Represents different levels of pain/discomfort
 */
enum class PainLevel(val displayName: String, val level: Int, val description: String) {
    NONE("No Pain", 0, "No discomfort"),
    MILD("Mild", 1, "Slight discomfort, doesn't interfere with activities"),
    MODERATE("Moderate", 2, "Noticeable pain, some interference with activities"),
    SEVERE("Severe", 3, "Significant pain, interferes with daily activities"),
    EXTREME("Extreme", 4, "Debilitating pain, unable to perform normal activities");
    
    companion object {
        fun fromLevel(level: Int): PainLevel {
            return values().find { it.level == level } ?: NONE
        }
        
        fun getColorForLevel(level: Int): String {
            return when (level) {
                0 -> "#4CAF50" // Green
                1 -> "#8BC34A" // Light Green
                2 -> "#FFC107" // Amber
                3 -> "#FF9800" // Orange
                4 -> "#F44336" // Red
                else -> "#9E9E9E" // Gray
            }
        }
    }
}
