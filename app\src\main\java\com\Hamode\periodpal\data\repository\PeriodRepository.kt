package com.Hamode.periodpal.data.repository

import com.Hamode.periodpal.data.models.*
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

/**
 * Repository interface for period tracking data
 */
interface PeriodRepository {
    
    // User Profile
    suspend fun getUserProfile(): UserProfile?
    suspend fun saveUserProfile(profile: UserProfile)
    suspend fun updateUserProfile(profile: UserProfile)
    
    // Daily Logs
    suspend fun getDailyLog(date: LocalDate): DailyLog?
    suspend fun saveDailyLog(dailyLog: DailyLog)
    suspend fun updateDailyLog(dailyLog: DailyLog)
    suspend fun getDailyLogsInRange(startDate: LocalDate, endDate: LocalDate): List<DailyLog>
    fun observeDailyLogs(): Flow<List<DailyLog>>
    
    // Menstrual Cycles
    suspend fun getCurrentCycle(): MenstrualCycle?
    suspend fun getAllCycles(): List<MenstrualCycle>
    suspend fun saveCycle(cycle: Men<PERSON>rualCycle)
    suspend fun updateCycle(cycle: Menst<PERSON>alCycle)
    suspend fun getCyclesInRange(startDate: LocalDate, endDate: LocalDate): List<MenstrualCycle>
    fun observeCurrentCycle(): Flow<MenstrualCycle?>
    
    // Health Insights
    suspend fun getHealthInsights(): List<HealthInsight>
    suspend fun saveHealthInsight(insight: HealthInsight)
    suspend fun markInsightAsRead(insightId: String)
    suspend fun deleteHealthInsight(insightId: String)
    fun observeHealthInsights(): Flow<List<HealthInsight>>
    
    // Statistics and Analytics
    suspend fun getCycleStatistics(): CycleStatistics?
    suspend fun generateCyclePrediction(): CyclePrediction?
    suspend fun updateStatistics()
    
    // Utility Methods
    suspend fun startNewCycle(startDate: LocalDate): MenstrualCycle
    suspend fun endCurrentCycle(endDate: LocalDate): MenstrualCycle?
    suspend fun isOnPeriod(date: LocalDate = LocalDate.now()): Boolean
    suspend fun getCurrentCycleDay(): Int?
    suspend fun getCurrentPhase(): CyclePhase?
    suspend fun getNextPeriodPrediction(): LocalDate?
    suspend fun getFertileWindow(): Pair<LocalDate, LocalDate>?
    
    // Data Management
    suspend fun exportData(): String // JSON export
    suspend fun importData(jsonData: String): Boolean
    suspend fun clearAllData()
    suspend fun backupData(): Boolean
}
