package com.Hamode.periodpal.ui.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.components.*
import com.Hamode.periodpal.ui.screens.*
import java.time.LocalDate

@Composable
fun PeriodPalNavHost(
    currentDestination: NavigationDestination,
    onNavigate: (NavigationDestination) -> Unit,
    onNavigateToSecondary: (SecondaryDestination) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.fillMaxSize()) {
        // Main content based on current destination
        when (currentDestination) {
            NavigationDestination.HOME -> {
                MainScreen(
                    onNavigateToCalendar = { onNavigate(NavigationDestination.CALENDAR) },
                    onNavigateToLogs = { onNavigate(NavigationDestination.HEALTH_LOGS) },
                    onNavigateToInsights = { onNavigate(NavigationDestination.INSIGHTS) },
                    onNavigateToSettings = { onNavigateToSecondary(SecondaryDestination.SETTINGS) },
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            NavigationDestination.CALENDAR -> {
                CalendarScreen(
                    onNavigateToSymptomLogging = { date ->
                        onNavigateToSecondary(SecondaryDestination.SYMPTOM_LOGGING)
                    },
                    onNavigateBack = { onNavigate(NavigationDestination.HOME) },
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            NavigationDestination.HEALTH_LOGS -> {
                HealthLogsScreen(
                    onNavigateToSymptomLogging = { date ->
                        onNavigateToSecondary(SecondaryDestination.SYMPTOM_LOGGING)
                    },
                    onNavigateToHistory = { onNavigateToSecondary(SecondaryDestination.CYCLE_HISTORY) },
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            NavigationDestination.INSIGHTS -> {
                HealthInsightsScreen(
                    cycleStatistics = getSampleCycleStatistics(),
                    cyclePrediction = getSampleCyclePrediction(),
                    healthInsights = getSampleHealthInsights(),
                    onBack = { onNavigate(NavigationDestination.HOME) },
                    onRefreshInsights = { /* Refresh insights */ },
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            NavigationDestination.PROFILE -> {
                ProfileScreen(
                    onNavigateToSettings = { onNavigateToSecondary(SecondaryDestination.SETTINGS) },
                    onNavigateToEmergencyContacts = { onNavigateToSecondary(SecondaryDestination.EMERGENCY_CONTACTS) },
                    onNavigateToExportData = { onNavigateToSecondary(SecondaryDestination.EXPORT_DATA) },
                    onNavigateToAbout = { onNavigateToSecondary(SecondaryDestination.ABOUT) },
                    onNavigateToHelp = { onNavigateToSecondary(SecondaryDestination.HELP) },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
        
        // Bottom Navigation
        PeriodPalBottomNavigation(
            currentDestination = currentDestination,
            onNavigate = onNavigate,
            notificationCounts = getNotificationCounts(),
            modifier = Modifier.align(Alignment.BottomCenter)
        )
        
        // Floating Action Button (visible on certain screens)
        if (shouldShowFAB(currentDestination)) {
            FloatingActionButton(
                onClick = { onNavigateToSecondary(SecondaryDestination.SYMPTOM_LOGGING) },
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 16.dp, bottom = 88.dp) // Above bottom nav
            )
        }
    }
}

@Composable
private fun shouldShowFAB(destination: NavigationDestination): Boolean {
    return when (destination) {
        NavigationDestination.HOME,
        NavigationDestination.CALENDAR,
        NavigationDestination.HEALTH_LOGS -> true
        else -> false
    }
}

@Composable
private fun getNotificationCounts(): Map<NavigationDestination, Int> {
    // In a real app, this would come from your data layer
    return mapOf(
        NavigationDestination.HEALTH_LOGS to 2, // Pending logs
        NavigationDestination.INSIGHTS to 1 // New insights available
    )
}

// Sample data functions (in a real app, these would come from your data layer)
@Composable
private fun getSampleCycleStatistics(): CycleStatistics {
    return CycleStatistics(
        averageCycleLength = 28.5,
        averagePeriodLength = 5.2,
        cycleVariability = 2.1,
        totalCycles = 12,
        longestCycle = 32,
        shortestCycle = 26,
        mostCommonSymptoms = listOf(
            Pair(SymptomType.CRAMPS, 8),
            Pair(SymptomType.BLOATING, 6),
            Pair(SymptomType.HEADACHE, 4)
        ),
        averagePainLevel = 2.3,
        moodTrends = mapOf(
            MoodType.HAPPY to 15,
            MoodType.IRRITABLE to 8,
            MoodType.TIRED to 12
        )
    )
}

@Composable
private fun getSampleCyclePrediction(): CyclePrediction {
    return CyclePrediction(
        nextPeriodStart = LocalDate.now().plusDays(12),
        nextPeriodEnd = LocalDate.now().plusDays(17),
        nextFertileWindowStart = LocalDate.now().plusDays(26),
        nextFertileWindowEnd = LocalDate.now().plusDays(30),
        nextPMSStart = LocalDate.now().plusDays(5),
        confidence = 0.85,
        basedOnCycles = 12
    )
}

@Composable
private fun getSampleHealthInsights(): List<HealthInsight> {
    return listOf(
        HealthInsight(
            id = "1",
            type = InsightType.CYCLE_PATTERN,
            title = "Regular Cycle Pattern",
            description = "Your cycles have been very consistent over the past 6 months.",
            recommendation = "Continue your current lifestyle habits as they seem to support cycle regularity.",
            severity = InsightSeverity.INFO
        ),
        HealthInsight(
            id = "2",
            type = InsightType.SYMPTOM_TREND,
            title = "Frequent Headaches",
            description = "You've reported headaches in 60% of your recent cycles.",
            recommendation = "Consider tracking potential triggers like stress, sleep, or diet. Consult your healthcare provider if headaches persist.",
            severity = InsightSeverity.MEDIUM
        )
    )
}
