package com.Hamode.periodpal.data.models

import java.time.LocalDate

/**
 * Represents health insights and analytics
 */
data class HealthInsight(
    val id: String = "",
    val type: InsightType,
    val title: String,
    val description: String,
    val recommendation: String = "",
    val severity: InsightSeverity = InsightSeverity.INFO,
    val dateGenerated: LocalDate = LocalDate.now(),
    val isRead: Boolean = false,
    val relatedData: Map<String, Any> = emptyMap()
)

/**
 * Types of health insights
 */
enum class InsightType(val displayName: String) {
    CYCLE_PATTERN("Cycle Pattern"),
    SYMPTOM_TREND("Symptom Trend"),
    MOOD_PATTERN("Mood Pattern"),
    PAIN_ANALYSIS("Pain Analysis"),
    FERTILITY_INSIGHT("Fertility Insight"),
    HEALTH_RECOMMENDATION("Health Recommendation"),
    CYCLE_IRREGULARITY("Cycle Irregularity"),
    SYMPTOM_CORRELATION("Symptom Correlation")
}

/**
 * Severity levels for insights
 */
enum class InsightSeverity(val displayName: String, val color: String) {
    INFO("Information", "#2196F3"),
    LOW("Low Priority", "#4CAF50"),
    MEDIUM("Medium Priority", "#FF9800"),
    HIGH("High Priority", "#F44336"),
    URGENT("Urgent", "#9C27B0")
}


