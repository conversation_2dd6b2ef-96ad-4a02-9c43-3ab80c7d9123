package com.Hamode.periodpal.ui

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.Hamode.periodpal.ui.navigation.*
import com.Hamode.periodpal.ui.screens.SymptomLoggingScreen
import com.Hamode.periodpal.ui.theme.PeriodPalTheme
import java.time.LocalDate

@Composable
fun PeriodPalApp(
    modifier: Modifier = Modifier
) {
    var currentDestination by remember { mutableStateOf(NavigationDestination.HOME) }
    var currentSecondaryDestination by remember { mutableStateOf<SecondaryDestination?>(null) }
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    
    Surface(
        modifier = modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        when (currentSecondaryDestination) {
            SecondaryDestination.SYMPTOM_LOGGING -> {
                SymptomLoggingScreen(
                    selectedDate = selectedDate,
                    initialDailyLog = null,
                    onSave = { dailyLog ->
                        // Save the daily log
                        // In a real app, this would save to your data layer
                        currentSecondaryDestination = null
                    },
                    onBack = {
                        currentSecondaryDestination = null
                    }
                )
            }
            
            SecondaryDestination.EMERGENCY_CONTACTS -> {
                EmergencyContactsScreen(
                    onBack = { currentSecondaryDestination = null }
                )
            }
            
            SecondaryDestination.SETTINGS -> {
                SettingsScreen(
                    onBack = { currentSecondaryDestination = null }
                )
            }
            
            SecondaryDestination.CYCLE_HISTORY -> {
                CycleHistoryScreen(
                    onBack = { currentSecondaryDestination = null }
                )
            }
            
            SecondaryDestination.EXPORT_DATA -> {
                ExportDataScreen(
                    onBack = { currentSecondaryDestination = null }
                )
            }
            
            SecondaryDestination.ABOUT -> {
                AboutScreen(
                    onBack = { currentSecondaryDestination = null }
                )
            }
            
            SecondaryDestination.HELP -> {
                HelpScreen(
                    onBack = { currentSecondaryDestination = null }
                )
            }
            
            null -> {
                // Main navigation
                PeriodPalNavHost(
                    currentDestination = currentDestination,
                    onNavigate = { destination ->
                        currentDestination = destination
                    },
                    onNavigateToSecondary = { secondaryDestination ->
                        if (secondaryDestination == SecondaryDestination.SYMPTOM_LOGGING) {
                            selectedDate = LocalDate.now()
                        }
                        currentSecondaryDestination = secondaryDestination
                    }
                )
            }
        }
    }
}

// Placeholder screens for secondary destinations
@Composable
private fun EmergencyContactsScreen(onBack: () -> Unit) {
    PlaceholderScreen(
        title = "Emergency Contacts",
        description = "Manage your emergency contacts and healthcare providers",
        onBack = onBack
    )
}

@Composable
private fun SettingsScreen(onBack: () -> Unit) {
    PlaceholderScreen(
        title = "Settings",
        description = "App preferences, notifications, and privacy settings",
        onBack = onBack
    )
}

@Composable
private fun CycleHistoryScreen(onBack: () -> Unit) {
    PlaceholderScreen(
        title = "Cycle History",
        description = "View your complete cycle tracking history",
        onBack = onBack
    )
}

@Composable
private fun ExportDataScreen(onBack: () -> Unit) {
    PlaceholderScreen(
        title = "Export Data",
        description = "Download your health data in various formats",
        onBack = onBack
    )
}

@Composable
private fun AboutScreen(onBack: () -> Unit) {
    PlaceholderScreen(
        title = "About PeriodPal",
        description = "Version information, privacy policy, and terms of service",
        onBack = onBack
    )
}

@Composable
private fun HelpScreen(onBack: () -> Unit) {
    PlaceholderScreen(
        title = "Help & Support",
        description = "FAQs, tutorials, and contact support",
        onBack = onBack
    )
}

@Composable
private fun PlaceholderScreen(
    title: String,
    description: String,
    onBack: () -> Unit
) {
    androidx.compose.foundation.layout.Column(
        modifier = Modifier.fillMaxSize()
    ) {
        androidx.compose.material3.TopAppBar(
            title = {
                androidx.compose.material3.Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.SemiBold
                )
            },
            navigationIcon = {
                androidx.compose.material3.IconButton(onClick = onBack) {
                    androidx.compose.material3.Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            colors = androidx.compose.material3.TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )
        
        androidx.compose.foundation.layout.Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            androidx.compose.foundation.layout.Column(
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
            ) {
                androidx.compose.material3.Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(8.dp))
                
                androidx.compose.material3.Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = androidx.compose.ui.graphics.Color.Gray,
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
                
                androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(16.dp))
                
                androidx.compose.material3.Text(
                    text = "Coming Soon!",
                    style = MaterialTheme.typography.titleMedium,
                    color = com.Hamode.periodpal.ui.theme.RosePink40
                )
            }
        }
    }
}

@Preview(showBackground = true, name = "PeriodPal App Preview")
@Composable
fun PeriodPalAppPreview() {
    PeriodPalTheme {
        PeriodPalApp()
    }
}
