package com.Hamode.periodpal.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.theme.*

@Composable
fun EnhancedFlowIntensitySelector(
    selectedIntensity: FlowIntensity,
    onIntensitySelected: (FlowIntensity) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Flow Intensity",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(FlowIntensity.values()) { intensity ->
                    EnhancedFlowIntensityChip(
                        intensity = intensity,
                        isSelected = selectedIntensity == intensity,
                        onClick = { onIntensitySelected(intensity) }
                    )
                }
            }
            
            // Description
            if (selectedIntensity != FlowIntensity.NONE) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = selectedIntensity.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = DarkGray.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun EnhancedFlowIntensityChip(
    intensity: FlowIntensity,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        label = "scale"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            PeriodPalThemeColors.getFlowColor(intensity)
        } else {
            LightGray.copy(alpha = 0.5f)
        },
        label = "backgroundColor"
    )
    
    val textColor = if (isSelected) {
        if (intensity == FlowIntensity.NONE || intensity == FlowIntensity.SPOTTING) {
            DarkGray
        } else {
            White
        }
    } else {
        DarkGray
    }
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .scale(scale)
            .clip(RoundedCornerShape(16.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .width(80.dp)
    ) {
        // Visual indicator (dots representing flow)
        Row(
            horizontalArrangement = Arrangement.spacedBy(2.dp),
            modifier = Modifier.padding(bottom = 8.dp)
        ) {
            repeat(intensity.level.coerceAtMost(5)) { index ->
                Box(
                    modifier = Modifier
                        .size(6.dp)
                        .clip(CircleShape)
                        .background(
                            if (isSelected) {
                                if (intensity == FlowIntensity.NONE) DarkGray else White
                            } else {
                                PeriodPalThemeColors.getFlowColor(intensity)
                            }
                        )
                )
            }
        }
        
        Text(
            text = intensity.displayName,
            style = MaterialTheme.typography.labelMedium,
            color = textColor,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun EnhancedMoodSelector(
    selectedMoods: Set<MoodType>,
    onMoodsChanged: (Set<MoodType>) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "How are you feeling?",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // Positive Moods
            Text(
                text = "Positive",
                style = MaterialTheme.typography.labelMedium,
                color = SuccessGreen,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(MoodType.getPositiveMoods()) { mood ->
                    EnhancedMoodChip(
                        mood = mood,
                        isSelected = selectedMoods.contains(mood),
                        onClick = {
                            val newMoods = if (selectedMoods.contains(mood)) {
                                selectedMoods - mood
                            } else {
                                selectedMoods + mood
                            }
                            onMoodsChanged(newMoods)
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Negative Moods
            Text(
                text = "Challenging",
                style = MaterialTheme.typography.labelMedium,
                color = WarningOrange,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(MoodType.getNegativeMoods()) { mood ->
                    EnhancedMoodChip(
                        mood = mood,
                        isSelected = selectedMoods.contains(mood),
                        onClick = {
                            val newMoods = if (selectedMoods.contains(mood)) {
                                selectedMoods - mood
                            } else {
                                selectedMoods + mood
                            }
                            onMoodsChanged(newMoods)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun EnhancedMoodChip(
    mood: MoodType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        label = "scale"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            PeriodPalThemeColors.getMoodColor(mood).copy(alpha = 0.2f)
        } else {
            LightGray.copy(alpha = 0.3f)
        },
        label = "backgroundColor"
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (isSelected) {
            PeriodPalThemeColors.getMoodColor(mood)
        } else {
            Color.Transparent
        },
        label = "borderColor"
    )
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .scale(scale)
            .clip(RoundedCornerShape(16.dp))
            .background(backgroundColor)
            .border(
                width = if (isSelected) 2.dp else 0.dp,
                color = borderColor,
                shape = RoundedCornerShape(16.dp)
            )
            .clickable { onClick() }
            .padding(12.dp)
            .width(70.dp)
    ) {
        Text(
            text = mood.emoji,
            fontSize = 28.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        Text(
            text = mood.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) {
                PeriodPalThemeColors.getMoodColor(mood)
            } else {
                DarkGray
            },
            textAlign = TextAlign.Center,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
            maxLines = 1
        )
    }
}

@Composable
fun EnhancedPainLevelSelector(
    selectedPainLevel: PainLevel,
    onPainLevelChanged: (PainLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Pain Level",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                PainLevel.values().forEach { painLevel ->
                    EnhancedPainLevelIndicator(
                        painLevel = painLevel,
                        isSelected = selectedPainLevel == painLevel,
                        onClick = { onPainLevelChanged(painLevel) }
                    )
                }
            }
            
            // Description
            if (selectedPainLevel != PainLevel.NONE) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = selectedPainLevel.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = DarkGray.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun EnhancedPainLevelIndicator(
    painLevel: PainLevel,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.2f else 1f,
        label = "scale"
    )
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable { onClick() }
            .padding(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(if (isSelected) 40.dp else 32.dp)
                .scale(scale)
                .clip(CircleShape)
                .background(
                    brush = if (isSelected) {
                        Brush.radialGradient(
                            colors = listOf(
                                PeriodPalThemeColors.getPainColor(painLevel),
                                PeriodPalThemeColors.getPainColor(painLevel).copy(alpha = 0.7f)
                            )
                        )
                    } else {
                        Brush.radialGradient(
                            colors = listOf(
                                PeriodPalThemeColors.getPainColor(painLevel).copy(alpha = 0.3f),
                                PeriodPalThemeColors.getPainColor(painLevel).copy(alpha = 0.1f)
                            )
                        )
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = painLevel.level.toString(),
                style = MaterialTheme.typography.labelMedium,
                color = if (isSelected) White else DarkGray,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = painLevel.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) PeriodPalThemeColors.getPainColor(painLevel) else DarkGray,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true, name = "Enhanced Flow Intensity Selector Preview")
@Composable
fun EnhancedFlowIntensitySelectorPreview() {
    PeriodPalTheme {
        EnhancedFlowIntensitySelector(
            selectedIntensity = FlowIntensity.MEDIUM,
            onIntensitySelected = { }
        )
    }
}

@Preview(showBackground = true, name = "Enhanced Mood Selector Preview")
@Composable
fun EnhancedMoodSelectorPreview() {
    PeriodPalTheme {
        EnhancedMoodSelector(
            selectedMoods = setOf(MoodType.HAPPY, MoodType.ANXIOUS),
            onMoodsChanged = { }
        )
    }
}

@Preview(showBackground = true, name = "Enhanced Pain Level Selector Preview")
@Composable
fun EnhancedPainLevelSelectorPreview() {
    PeriodPalTheme {
        EnhancedPainLevelSelector(
            selectedPainLevel = PainLevel.MODERATE,
            onPainLevelChanged = { }
        )
    }
}
