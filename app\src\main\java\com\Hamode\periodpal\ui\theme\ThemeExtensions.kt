package com.Hamode.periodpal.ui.theme

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import com.Hamode.periodpal.data.models.CyclePhase
import com.Hamode.periodpal.data.models.FlowIntensity
import com.Hamode.periodpal.data.models.MoodType
import com.Hamode.periodpal.data.models.PainLevel

/**
 * Extended color palette for PeriodPal theme
 */
data class PeriodPalColors(
    val rosePink80: Color = RosePink80,
    val rosePink60: Color = RosePink60,
    val rosePink40: Color = RosePink40,
    val rosePink20: Color = RosePink20,
    val softPink80: Color = SoftPink80,
    val softPink60: Color = SoftPink60,
    val softPink40: Color = SoftPink40,
    val softPink20: Color = SoftPink20,
    val fertileGreen: Color = FertileGreen,
    val pmsColor: Color = PMSPurple,
    val periodRed: Color = PeriodRed,
    val ovulationBlue: Color = OvulationBlue,
    val successGreen: Color = SuccessGreen,
    val warningOrange: Color = WarningOrange,
    val errorRed: Color = ErrorRed,
    val infoBlue: Color = InfoBlue
)

/**
 * Extension functions for getting colors based on cycle data
 */
object PeriodPalThemeColors {
    
    /**
     * Gets color for cycle phase
     */
    @Composable
    @ReadOnlyComposable
    fun getPhaseColor(phase: CyclePhase): Color {
        return when (phase) {
            CyclePhase.MENSTRUAL -> PeriodRed
            CyclePhase.FOLLICULAR -> SoftPink40
            CyclePhase.OVULATION -> FertileGreen
            CyclePhase.LUTEAL -> PMSPurple
        }
    }
    
    /**
     * Gets color for flow intensity
     */
    @Composable
    @ReadOnlyComposable
    fun getFlowColor(intensity: FlowIntensity): Color {
        return when (intensity) {
            FlowIntensity.NONE -> LightGray
            FlowIntensity.SPOTTING -> SoftPink80
            FlowIntensity.LIGHT -> SoftPink60
            FlowIntensity.MEDIUM -> RosePink40
            FlowIntensity.HEAVY -> PeriodRed
            FlowIntensity.VERY_HEAVY -> Color(0xFFB71C1C) // Dark red
        }
    }
    
    /**
     * Gets color for pain level
     */
    @Composable
    @ReadOnlyComposable
    fun getPainColor(painLevel: PainLevel): Color {
        return when (painLevel) {
            PainLevel.NONE -> SuccessGreen
            PainLevel.MILD -> Color(0xFF8BC34A) // Light Green
            PainLevel.MODERATE -> WarningOrange
            PainLevel.SEVERE -> Color(0xFFFF5722) // Deep Orange
            PainLevel.EXTREME -> ErrorRed
        }
    }
    
    /**
     * Gets color for mood type
     */
    @Composable
    @ReadOnlyComposable
    fun getMoodColor(mood: MoodType): Color {
        return when (mood) {
            MoodType.HAPPY -> Color(0xFFFFEB3B) // Yellow
            MoodType.SAD -> Color(0xFF2196F3) // Blue
            MoodType.ANXIOUS -> Color(0xFFFF9800) // Orange
            MoodType.IRRITABLE -> Color(0xFFF44336) // Red
            MoodType.CALM -> Color(0xFF4CAF50) // Green
            MoodType.ENERGETIC -> Color(0xFFFF5722) // Deep Orange
            MoodType.TIRED -> Color(0xFF9C27B0) // Purple
            MoodType.EMOTIONAL -> Color(0xFFE91E63) // Pink
            MoodType.CONFIDENT -> Color(0xFF00BCD4) // Cyan
            MoodType.STRESSED -> Color(0xFF795548) // Brown
        }
    }
    
    /**
     * Gets background color for calendar days based on cycle phase
     */
    @Composable
    @ReadOnlyComposable
    fun getCalendarDayBackground(phase: CyclePhase?, isToday: Boolean = false): Color {
        return when {
            isToday -> RosePink40
            phase == CyclePhase.MENSTRUAL -> PeriodRed.copy(alpha = 0.2f)
            phase == CyclePhase.OVULATION -> FertileGreen.copy(alpha = 0.2f)
            phase == CyclePhase.LUTEAL -> PMSPurple.copy(alpha = 0.1f)
            else -> Color.Transparent
        }
    }
    
    /**
     * Gets text color for calendar days
     */
    @Composable
    @ReadOnlyComposable
    fun getCalendarDayTextColor(phase: CyclePhase?, isToday: Boolean = false): Color {
        return when {
            isToday -> White
            phase == CyclePhase.MENSTRUAL -> PeriodRed
            phase == CyclePhase.OVULATION -> FertileGreen
            phase == CyclePhase.LUTEAL -> PMSPurple
            else -> DarkGray
        }
    }
    
    /**
     * Gets gradient colors for cycle visualization
     */
    @Composable
    @ReadOnlyComposable
    fun getCycleGradient(): List<Color> {
        return listOf(
            PeriodRed,
            SoftPink40,
            FertileGreen,
            PMSPurple
        )
    }
    
    /**
     * Gets surface color with appropriate elevation
     */
    @Composable
    @ReadOnlyComposable
    fun getSurfaceColor(elevation: Int = 0): Color {
        return when (elevation) {
            0 -> White
            1 -> Color(0xFFFFFBFE)
            2 -> Color(0xFFF8F8F8)
            3 -> Color(0xFFF5F5F5)
            else -> LightGray
        }
    }
}

/**
 * Enhanced accessibility-focused color utilities
 */
object AccessibilityColors {

    /**
     * Ensures proper contrast ratio for text on background (WCAG AA compliant)
     */
    @Composable
    @ReadOnlyComposable
    fun getContrastingTextColor(backgroundColor: Color): Color {
        // Calculate relative luminance using WCAG formula
        val luminance = calculateRelativeLuminance(backgroundColor)

        // WCAG AA requires 4.5:1 contrast ratio for normal text
        return if (luminance > 0.179) DarkGray else White
    }

    /**
     * Gets high contrast version of a color for accessibility
     */
    @Composable
    @ReadOnlyComposable
    fun getHighContrastColor(color: Color): Color {
        return when (color) {
            RosePink40 -> Color(0xFF8E0038) // Much darker pink
            SoftPink40 -> Color(0xFF8E0038) // Much darker pink
            FertileGreen -> Color(0xFF1B5E20) // Much darker green
            PMSPurple -> Color(0xFF4A148C) // Much darker purple
            InfoBlue -> Color(0xFF0D47A1) // Much darker blue
            WarningOrange -> Color(0xFFE65100) // Much darker orange
            ErrorRed -> Color(0xFFB71C1C) // Much darker red
            SuccessGreen -> Color(0xFF1B5E20) // Much darker green
            else -> color
        }
    }

    /**
     * Gets focus indicator color for accessibility (high contrast)
     */
    @Composable
    @ReadOnlyComposable
    fun getFocusIndicatorColor(): Color = Color(0xFF000080) // Navy blue for high contrast

    /**
     * Gets selection color for accessibility
     */
    @Composable
    @ReadOnlyComposable
    fun getSelectionColor(): Color = Color(0xFF000080).copy(alpha = 0.2f)

    /**
     * Gets error color with proper contrast
     */
    @Composable
    @ReadOnlyComposable
    fun getAccessibleErrorColor(): Color = Color(0xFFD32F2F)

    /**
     * Gets success color with proper contrast
     */
    @Composable
    @ReadOnlyComposable
    fun getAccessibleSuccessColor(): Color = Color(0xFF388E3C)

    /**
     * Gets warning color with proper contrast
     */
    @Composable
    @ReadOnlyComposable
    fun getAccessibleWarningColor(): Color = Color(0xFFF57C00)

    /**
     * Calculates relative luminance according to WCAG guidelines
     */
    private fun calculateRelativeLuminance(color: Color): Double {
        fun linearize(component: Float): Double {
            return if (component <= 0.03928) {
                component / 12.92
            } else {
                kotlin.math.pow((component + 0.055) / 1.055, 2.4)
            }
        }

        val r = linearize(color.red)
        val g = linearize(color.green)
        val b = linearize(color.blue)

        return 0.2126 * r + 0.7152 * g + 0.0722 * b
    }

    /**
     * Calculates contrast ratio between two colors
     */
    fun calculateContrastRatio(color1: Color, color2: Color): Double {
        val lum1 = calculateRelativeLuminance(color1)
        val lum2 = calculateRelativeLuminance(color2)

        val lighter = maxOf(lum1, lum2)
        val darker = minOf(lum1, lum2)

        return (lighter + 0.05) / (darker + 0.05)
    }

    /**
     * Checks if color combination meets WCAG AA standards (4.5:1 ratio)
     */
    fun meetsWCAGAA(foreground: Color, background: Color): Boolean {
        return calculateContrastRatio(foreground, background) >= 4.5
    }

    /**
     * Checks if color combination meets WCAG AAA standards (7:1 ratio)
     */
    fun meetsWCAGAAA(foreground: Color, background: Color): Boolean {
        return calculateContrastRatio(foreground, background) >= 7.0
    }
}

/**
 * Animation and state colors
 */
object StateColors {
    
    /**
     * Gets color for pressed state
     */
    @Composable
    @ReadOnlyComposable
    fun getPressedColor(baseColor: Color): Color = baseColor.copy(alpha = 0.8f)
    
    /**
     * Gets color for disabled state
     */
    @Composable
    @ReadOnlyComposable
    fun getDisabledColor(): Color = MediumGray
    
    /**
     * Gets color for loading state
     */
    @Composable
    @ReadOnlyComposable
    fun getLoadingColor(): Color = RosePink40.copy(alpha = 0.6f)
    
    /**
     * Gets color for success state
     */
    @Composable
    @ReadOnlyComposable
    fun getSuccessColor(): Color = SuccessGreen
    
    /**
     * Gets color for warning state
     */
    @Composable
    @ReadOnlyComposable
    fun getWarningColor(): Color = WarningOrange
    
    /**
     * Gets color for error state
     */
    @Composable
    @ReadOnlyComposable
    fun getErrorColor(): Color = ErrorRed
}
