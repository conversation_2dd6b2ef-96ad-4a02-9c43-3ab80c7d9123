package com.Hamode.periodpal.data.models

/**
 * Represents different levels of menstrual flow intensity
 */
enum class FlowIntensity(val displayName: String, val description: String, val level: Int) {
    NONE("None", "No flow", 0),
    SPOTTING("Spotting", "Very light spotting", 1),
    LIGHT("Light", "Light flow", 2),
    MEDIUM("Medium", "Normal flow", 3),
    HEAVY("Heavy", "Heavy flow", 4),
    VERY_HEAVY("Very Heavy", "Extremely heavy flow", 5);
    
    companion object {
        fun fromLevel(level: Int): FlowIntensity {
            return values().find { it.level == level } ?: NONE
        }
    }
}
