package com.Hamode.periodpal.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun QuickSymptomLogger(
    selectedDate: LocalDate,
    currentLog: DailyLog?,
    onQuickLog: (DailyLog) -> Unit,
    onOpenFullLogger: () -> Unit,
    modifier: Modifier = Modifier
) {
    var quickFlowIntensity by remember { mutableStateOf(currentLog?.flowIntensity ?: FlowIntensity.NONE) }
    var quickMood by remember { mutableStateOf(currentLog?.moods?.firstOrNull()) }
    var quickPainLevel by remember { mutableStateOf(currentLog?.painLevel ?: PainLevel.NONE) }
    var quickSymptoms by remember { mutableStateOf(currentLog?.symptoms?.take(3)?.toSet() ?: emptySet()) }
    
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Quick Log",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = selectedDate.format(DateTimeFormatter.ofPattern("MMM dd")),
                        style = MaterialTheme.typography.bodySmall,
                        color = RosePink40
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Save Quick Log Button
                    if (quickFlowIntensity != FlowIntensity.NONE || 
                        quickMood != null || 
                        quickPainLevel != PainLevel.NONE || 
                        quickSymptoms.isNotEmpty()) {
                        
                        Button(
                            onClick = {
                                val quickLog = DailyLog(
                                    date = selectedDate,
                                    flowIntensity = quickFlowIntensity,
                                    moods = listOfNotNull(quickMood),
                                    painLevel = quickPainLevel,
                                    symptoms = quickSymptoms.toList()
                                )
                                onQuickLog(quickLog)
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = RosePink40
                            ),
                            shape = RoundedCornerShape(8.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
                        ) {
                            Text(
                                text = "Save",
                                style = MaterialTheme.typography.labelMedium,
                                color = White
                            )
                        }
                    }
                    
                    // Full Logger Button
                    IconButton(
                        onClick = onOpenFullLogger,
                        modifier = Modifier
                            .size(32.dp)
                            .background(
                                color = RosePink40.copy(alpha = 0.1f),
                                shape = CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Open full logger",
                            tint = RosePink40,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Quick Flow Selection
            QuickFlowSelector(
                selectedIntensity = quickFlowIntensity,
                onIntensitySelected = { quickFlowIntensity = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Quick Mood Selection
            QuickMoodSelector(
                selectedMood = quickMood,
                onMoodSelected = { quickMood = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Quick Pain Selection
            QuickPainSelector(
                selectedPainLevel = quickPainLevel,
                onPainLevelSelected = { quickPainLevel = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Quick Symptoms
            QuickSymptomsGrid(
                selectedSymptoms = quickSymptoms,
                onSymptomsChanged = { quickSymptoms = it },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun QuickFlowSelector(
    selectedIntensity: FlowIntensity,
    onIntensitySelected: (FlowIntensity) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "Flow",
            style = MaterialTheme.typography.labelMedium,
            color = DarkGray.copy(alpha = 0.7f),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 6.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            items(FlowIntensity.values().take(4)) { intensity ->
                QuickFlowChip(
                    intensity = intensity,
                    isSelected = selectedIntensity == intensity,
                    onClick = { onIntensitySelected(intensity) }
                )
            }
        }
    }
}

@Composable
private fun QuickFlowChip(
    intensity: FlowIntensity,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            PeriodPalThemeColors.getFlowColor(intensity)
        } else {
            LightGray.copy(alpha = 0.3f)
        },
        label = "backgroundColor"
    )
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 10.dp, vertical = 6.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = intensity.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected && intensity.level > 1) White else DarkGray,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

@Composable
private fun QuickMoodSelector(
    selectedMood: MoodType?,
    onMoodSelected: (MoodType?) -> Unit,
    modifier: Modifier = Modifier
) {
    val quickMoods = listOf(
        MoodType.HAPPY,
        MoodType.CALM,
        MoodType.TIRED,
        MoodType.IRRITABLE,
        MoodType.ANXIOUS
    )
    
    Column(modifier = modifier) {
        Text(
            text = "Mood",
            style = MaterialTheme.typography.labelMedium,
            color = DarkGray.copy(alpha = 0.7f),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 6.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            items(quickMoods) { mood ->
                QuickMoodChip(
                    mood = mood,
                    isSelected = selectedMood == mood,
                    onClick = { 
                        onMoodSelected(if (selectedMood == mood) null else mood)
                    }
                )
            }
        }
    }
}

@Composable
private fun QuickMoodChip(
    mood: MoodType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        label = "scale"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            PeriodPalThemeColors.getMoodColor(mood).copy(alpha = 0.2f)
        } else {
            LightGray.copy(alpha = 0.3f)
        },
        label = "backgroundColor"
    )
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .scale(scale)
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(8.dp)
            .width(50.dp)
    ) {
        Text(
            text = mood.emoji,
            fontSize = 20.sp
        )
        Text(
            text = mood.displayName.take(4),
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) PeriodPalThemeColors.getMoodColor(mood) else DarkGray,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

@Composable
private fun QuickPainSelector(
    selectedPainLevel: PainLevel,
    onPainLevelSelected: (PainLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "Pain Level",
            style = MaterialTheme.typography.labelMedium,
            color = DarkGray.copy(alpha = 0.7f),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 6.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            PainLevel.values().forEach { painLevel ->
                QuickPainIndicator(
                    painLevel = painLevel,
                    isSelected = selectedPainLevel == painLevel,
                    onClick = { onPainLevelSelected(painLevel) }
                )
            }
        }
    }
}

@Composable
private fun QuickPainIndicator(
    painLevel: PainLevel,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.2f else 1f,
        label = "scale"
    )
    
    Box(
        modifier = Modifier
            .size(24.dp)
            .scale(scale)
            .clip(CircleShape)
            .background(
                if (isSelected) {
                    PeriodPalThemeColors.getPainColor(painLevel)
                } else {
                    PeriodPalThemeColors.getPainColor(painLevel).copy(alpha = 0.3f)
                }
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = painLevel.level.toString(),
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) White else DarkGray,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
private fun QuickSymptomsGrid(
    selectedSymptoms: Set<SymptomType>,
    onSymptomsChanged: (Set<SymptomType>) -> Unit,
    modifier: Modifier = Modifier
) {
    val quickSymptoms = listOf(
        SymptomType.CRAMPS,
        SymptomType.BLOATING,
        SymptomType.HEADACHE,
        SymptomType.FATIGUE
    )
    
    Column(modifier = modifier) {
        Text(
            text = "Common Symptoms",
            style = MaterialTheme.typography.labelMedium,
            color = DarkGray.copy(alpha = 0.7f),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 6.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            quickSymptoms.forEach { symptom ->
                QuickSymptomChip(
                    symptom = symptom,
                    isSelected = selectedSymptoms.contains(symptom),
                    onClick = {
                        val newSymptoms = if (selectedSymptoms.contains(symptom)) {
                            selectedSymptoms - symptom
                        } else {
                            if (selectedSymptoms.size < 3) {
                                selectedSymptoms + symptom
                            } else {
                                selectedSymptoms
                            }
                        }
                        onSymptomsChanged(newSymptoms)
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun QuickSymptomChip(
    symptom: SymptomType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            RosePink40.copy(alpha = 0.2f)
        } else {
            LightGray.copy(alpha = 0.3f)
        },
        label = "backgroundColor"
    )
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(6.dp)
    ) {
        Text(
            text = symptom.icon,
            fontSize = 16.sp
        )
        Text(
            text = symptom.displayName.take(6),
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) RosePink40 else DarkGray,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

@Preview(showBackground = true, name = "Quick Symptom Logger Preview")
@Composable
fun QuickSymptomLoggerPreview() {
    PeriodPalTheme {
        QuickSymptomLogger(
            selectedDate = LocalDate.now(),
            currentLog = null,
            onQuickLog = { },
            onOpenFullLogger = { }
        )
    }
}
