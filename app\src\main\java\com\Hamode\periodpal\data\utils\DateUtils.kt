package com.Hamode.periodpal.data.utils

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*

/**
 * Utility functions for date operations
 */
object DateUtils {
    
    private val displayFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy")
    private val shortFormatter = DateTimeFormatter.ofPattern("MMM dd")
    private val dayFormatter = DateTimeFormatter.ofPattern("dd")
    private val monthYearFormatter = DateTimeFormatter.ofPattern("MMMM yyyy")
    
    /**
     * Formats date for display (e.g., "Jan 15, 2024")
     */
    fun formatForDisplay(date: LocalDate): String {
        return date.format(displayFormatter)
    }
    
    /**
     * Formats date in short format (e.g., "Jan 15")
     */
    fun formatShort(date: LocalDate): String {
        return date.format(shortFormatter)
    }
    
    /**
     * Gets day of month as string (e.g., "15")
     */
    fun getDayString(date: LocalDate): String {
        return date.format(dayFormatter)
    }
    
    /**
     * Formats month and year (e.g., "January 2024")
     */
    fun formatMonthYear(date: LocalDate): String {
        return date.format(monthYearFormatter)
    }
    
    /**
     * Gets relative date description (e.g., "Today", "Yesterday", "3 days ago")
     */
    fun getRelativeDescription(date: LocalDate, referenceDate: LocalDate = LocalDate.now()): String {
        val daysDiff = ChronoUnit.DAYS.between(date, referenceDate).toInt()
        
        return when {
            daysDiff == 0 -> "Today"
            daysDiff == 1 -> "Yesterday"
            daysDiff == -1 -> "Tomorrow"
            daysDiff > 1 -> "$daysDiff days ago"
            daysDiff < -1 -> "In ${-daysDiff} days"
            else -> formatForDisplay(date)
        }
    }
    
    /**
     * Gets days between two dates
     */
    fun daysBetween(startDate: LocalDate, endDate: LocalDate): Int {
        return ChronoUnit.DAYS.between(startDate, endDate).toInt()
    }
    
    /**
     * Gets the start of the week for a given date (Monday)
     */
    fun getWeekStart(date: LocalDate): LocalDate {
        return date.minusDays((date.dayOfWeek.value - 1).toLong())
    }
    
    /**
     * Gets the end of the week for a given date (Sunday)
     */
    fun getWeekEnd(date: LocalDate): LocalDate {
        return date.plusDays((7 - date.dayOfWeek.value).toLong())
    }
    
    /**
     * Gets the start of the month for a given date
     */
    fun getMonthStart(date: LocalDate): LocalDate {
        return date.withDayOfMonth(1)
    }
    
    /**
     * Gets the end of the month for a given date
     */
    fun getMonthEnd(date: LocalDate): LocalDate {
        return date.withDayOfMonth(date.lengthOfMonth())
    }
    
    /**
     * Gets all dates in a month
     */
    fun getDatesInMonth(date: LocalDate): List<LocalDate> {
        val start = getMonthStart(date)
        val end = getMonthEnd(date)
        val dates = mutableListOf<LocalDate>()
        
        var current = start
        while (!current.isAfter(end)) {
            dates.add(current)
            current = current.plusDays(1)
        }
        
        return dates
    }
    
    /**
     * Gets dates in a range (inclusive)
     */
    fun getDatesInRange(startDate: LocalDate, endDate: LocalDate): List<LocalDate> {
        val dates = mutableListOf<LocalDate>()
        var current = startDate
        
        while (!current.isAfter(endDate)) {
            dates.add(current)
            current = current.plusDays(1)
        }
        
        return dates
    }
    
    /**
     * Checks if a date is today
     */
    fun isToday(date: LocalDate): Boolean {
        return date == LocalDate.now()
    }
    
    /**
     * Checks if a date is in the past
     */
    fun isPast(date: LocalDate): Boolean {
        return date.isBefore(LocalDate.now())
    }
    
    /**
     * Checks if a date is in the future
     */
    fun isFuture(date: LocalDate): Boolean {
        return date.isAfter(LocalDate.now())
    }
    
    /**
     * Gets the ordinal suffix for a day (e.g., "1st", "2nd", "3rd", "4th")
     */
    fun getOrdinalSuffix(day: Int): String {
        return when {
            day in 11..13 -> "${day}th"
            day % 10 == 1 -> "${day}st"
            day % 10 == 2 -> "${day}nd"
            day % 10 == 3 -> "${day}rd"
            else -> "${day}th"
        }
    }
    
    /**
     * Gets a user-friendly description of cycle day
     */
    fun getCycleDayDescription(cycleDay: Int): String {
        return "Day ${getOrdinalSuffix(cycleDay)} of cycle"
    }
    
    /**
     * Gets calendar weeks for a month (including partial weeks)
     */
    fun getCalendarWeeksForMonth(date: LocalDate): List<List<LocalDate?>> {
        val monthStart = getMonthStart(date)
        val monthEnd = getMonthEnd(date)
        val calendarStart = getWeekStart(monthStart)
        val calendarEnd = getWeekEnd(monthEnd)
        
        val weeks = mutableListOf<List<LocalDate?>>()
        var current = calendarStart
        
        while (!current.isAfter(calendarEnd)) {
            val week = mutableListOf<LocalDate?>()
            repeat(7) { dayIndex ->
                val dayDate = current.plusDays(dayIndex.toLong())
                if (dayDate.month == date.month) {
                    week.add(dayDate)
                } else {
                    week.add(null) // Outside current month
                }
            }
            weeks.add(week)
            current = current.plusWeeks(1)
        }
        
        return weeks
    }
}
