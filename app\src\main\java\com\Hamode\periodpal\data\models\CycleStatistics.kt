package com.Hamode.periodpal.data.models

/**
 * Data class representing cycle statistics and analytics
 */
data class CycleStatistics(
    val averageCycleLength: Double,
    val averagePeriodLength: Double,
    val cycleVariability: Double,
    val totalCycles: Int,
    val longestCycle: Int,
    val shortestCycle: Int,
    val mostCommonSymptoms: List<Pair<SymptomType, Int>>,
    val averagePainLevel: Double,
    val moodTrends: Map<MoodType, Int>
) {
    /**
     * Determines if cycles are regular based on variability
     */
    fun areRegular(): Boolean {
        return cycleVariability <= 3.0 && totalCycles >= 3
    }
    
    /**
     * Gets a human-readable description of cycle regularity
     */
    fun getRegularityDescription(): String {
        return when {
            totalCycles < 3 -> "Insufficient Data"
            cycleVariability <= 2.0 -> "Very Regular"
            cycleVariability <= 3.0 -> "Regular"
            cycleVariability <= 5.0 -> "Somewhat Irregular"
            else -> "Irregular"
        }
    }
    
    /**
     * Gets symptoms that occur frequently and may need attention
     */
    fun getProblematicSymptoms(): List<SymptomType> {
        val threshold = totalCycles * 0.6 // 60% of cycles
        return mostCommonSymptoms
            .filter { it.second >= threshold }
            .map { it.first }
    }
    
    /**
     * Gets the most common mood during cycles
     */
    fun getMostCommonMood(): MoodType? {
        return moodTrends.maxByOrNull { it.value }?.key
    }
    
    /**
     * Calculates cycle length range
     */
    fun getCycleLengthRange(): IntRange {
        return shortestCycle..longestCycle
    }
    
    /**
     * Determines if pain levels are concerning
     */
    fun hasConcerningPainLevels(): Boolean {
        return averagePainLevel >= 3.0
    }
}
