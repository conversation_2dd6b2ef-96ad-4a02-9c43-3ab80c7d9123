package com.Hamode.periodpal.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.CyclePhase
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import kotlin.math.*

@Composable
fun CircularCalendarWidget(
    currentDate: LocalDate,
    cycleDay: Int,
    phase: CyclePhase,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier,
    cycleLength: Int = 28,
    cycleStartDate: LocalDate = LocalDate.now().minusDays(cycleDay.toLong() - 1),
    fertileWindow: Pair<LocalDate, LocalDate>? = null,
    pmsWindow: Pair<LocalDate, LocalDate>? = null,
    periodDays: List<LocalDate> = emptyList(),
    nextPeriodPrediction: LocalDate? = null
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Cycle Overview",
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Box(
                modifier = Modifier.size(280.dp),
                contentAlignment = Alignment.Center
            ) {
                // Circular Calendar
                CircularCalendar(
                    cycleDay = cycleDay,
                    cycleLength = cycleLength,
                    phase = phase,
                    cycleStartDate = cycleStartDate,
                    fertileWindow = fertileWindow,
                    pmsWindow = pmsWindow,
                    periodDays = periodDays,
                    nextPeriodPrediction = nextPeriodPrediction,
                    modifier = Modifier.fillMaxSize()
                )
                
                // Center Content
                CenterContent(
                    cycleDay = cycleDay,
                    phase = phase,
                    currentDate = currentDate
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Phase Legend
            PhaseLegend(
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun CircularCalendar(
    cycleDay: Int,
    cycleLength: Int,
    phase: CyclePhase,
    cycleStartDate: LocalDate,
    fertileWindow: Pair<LocalDate, LocalDate>?,
    pmsWindow: Pair<LocalDate, LocalDate>?,
    periodDays: List<LocalDate>,
    nextPeriodPrediction: LocalDate?,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    Canvas(modifier = modifier) {
        val center = size.center
        val radius = size.minDimension / 2 - 40.dp.toPx()
        
        // Draw background circle
        drawCircle(
            color = LightGray.copy(alpha = 0.3f),
            radius = radius,
            center = center,
            style = Stroke(width = 8.dp.toPx())
        )
        
        // Draw cycle phases
        drawCyclePhases(
            center = center,
            radius = radius,
            cycleLength = cycleLength,
            strokeWidth = 8.dp.toPx()
        )

        // Draw fertile window highlight
        fertileWindow?.let { (start, end) ->
            drawFertileWindow(
                center = center,
                radius = radius,
                cycleStartDate = cycleStartDate,
                fertileStart = start,
                fertileEnd = end,
                cycleLength = cycleLength
            )
        }

        // Draw PMS window highlight
        pmsWindow?.let { (start, end) ->
            drawPMSWindow(
                center = center,
                radius = radius,
                cycleStartDate = cycleStartDate,
                pmsStart = start,
                pmsEnd = end,
                cycleLength = cycleLength
            )
        }

        // Draw period days
        drawPeriodDays(
            center = center,
            radius = radius,
            cycleStartDate = cycleStartDate,
            periodDays = periodDays,
            cycleLength = cycleLength
        )

        // Draw next period prediction
        nextPeriodPrediction?.let { prediction ->
            drawNextPeriodPrediction(
                center = center,
                radius = radius,
                cycleStartDate = cycleStartDate,
                predictionDate = prediction,
                cycleLength = cycleLength
            )
        }

        // Draw current day indicator
        drawCurrentDayIndicator(
            center = center,
            radius = radius,
            cycleDay = cycleDay,
            cycleLength = cycleLength,
            phase = phase
        )

        // Draw day markers
        drawDayMarkers(
            center = center,
            radius = radius,
            cycleLength = cycleLength
        )
    }
}

private fun DrawScope.drawCyclePhases(
    center: Offset,
    radius: Float,
    cycleLength: Int,
    strokeWidth: Float
) {
    val anglePerDay = 360f / cycleLength
    
    // Menstrual phase (days 1-5)
    drawArc(
        color = PeriodRed.copy(alpha = 0.7f),
        startAngle = -90f,
        sweepAngle = anglePerDay * 5,
        useCenter = false,
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2),
        style = Stroke(width = strokeWidth)
    )
    
    // Follicular phase (days 6-13)
    drawArc(
        color = SoftPink40.copy(alpha = 0.7f),
        startAngle = -90f + anglePerDay * 5,
        sweepAngle = anglePerDay * 8,
        useCenter = false,
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2),
        style = Stroke(width = strokeWidth)
    )
    
    // Ovulation phase (days 14-16)
    drawArc(
        color = FertileGreen.copy(alpha = 0.7f),
        startAngle = -90f + anglePerDay * 13,
        sweepAngle = anglePerDay * 3,
        useCenter = false,
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2),
        style = Stroke(width = strokeWidth)
    )
    
    // Luteal phase (days 17-28)
    drawArc(
        color = PMSPurple.copy(alpha = 0.7f),
        startAngle = -90f + anglePerDay * 16,
        sweepAngle = anglePerDay * (cycleLength - 16),
        useCenter = false,
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2),
        style = Stroke(width = strokeWidth)
    )
}

private fun DrawScope.drawCurrentDayIndicator(
    center: Offset,
    radius: Float,
    cycleDay: Int,
    cycleLength: Int,
    phase: CyclePhase
) {
    val anglePerDay = 360f / cycleLength
    val currentAngle = -90f + anglePerDay * (cycleDay - 1)
    val angleRad = Math.toRadians(currentAngle.toDouble())
    
    val indicatorX = center.x + radius * cos(angleRad).toFloat()
    val indicatorY = center.y + radius * sin(angleRad).toFloat()
    
    // Draw indicator circle
    drawCircle(
        color = White,
        radius = 12.dp.toPx(),
        center = Offset(indicatorX, indicatorY)
    )
    
    drawCircle(
        color = PeriodPalThemeColors.getPhaseColor(phase),
        radius = 8.dp.toPx(),
        center = Offset(indicatorX, indicatorY)
    )
}

private fun DrawScope.drawDayMarkers(
    center: Offset,
    radius: Float,
    cycleLength: Int
) {
    val anglePerDay = 360f / cycleLength

    for (day in 1..cycleLength step 7) {
        val angle = -90f + anglePerDay * (day - 1)
        val angleRad = Math.toRadians(angle.toDouble())

        val markerX = center.x + (radius + 20.dp.toPx()) * cos(angleRad).toFloat()
        val markerY = center.y + (radius + 20.dp.toPx()) * sin(angleRad).toFloat()

        drawCircle(
            color = DarkGray.copy(alpha = 0.5f),
            radius = 2.dp.toPx(),
            center = Offset(markerX, markerY)
        )
    }
}

private fun DrawScope.drawFertileWindow(
    center: Offset,
    radius: Float,
    cycleStartDate: LocalDate,
    fertileStart: LocalDate,
    fertileEnd: LocalDate,
    cycleLength: Int
) {
    val anglePerDay = 360f / cycleLength
    val startDay = java.time.temporal.ChronoUnit.DAYS.between(cycleStartDate, fertileStart).toInt() + 1
    val endDay = java.time.temporal.ChronoUnit.DAYS.between(cycleStartDate, fertileEnd).toInt() + 1

    if (startDay > 0 && endDay <= cycleLength) {
        val startAngle = -90f + anglePerDay * (startDay - 1)
        val sweepAngle = anglePerDay * (endDay - startDay + 1)

        // Draw fertile window highlight
        drawArc(
            color = FertileGreen.copy(alpha = 0.3f),
            startAngle = startAngle,
            sweepAngle = sweepAngle,
            useCenter = false,
            topLeft = Offset(center.x - radius - 15.dp.toPx(), center.y - radius - 15.dp.toPx()),
            size = androidx.compose.ui.geometry.Size((radius + 15.dp.toPx()) * 2, (radius + 15.dp.toPx()) * 2),
            style = Stroke(width = 15.dp.toPx())
        )
    }
}

private fun DrawScope.drawPMSWindow(
    center: Offset,
    radius: Float,
    cycleStartDate: LocalDate,
    pmsStart: LocalDate,
    pmsEnd: LocalDate,
    cycleLength: Int
) {
    val anglePerDay = 360f / cycleLength
    val startDay = java.time.temporal.ChronoUnit.DAYS.between(cycleStartDate, pmsStart).toInt() + 1
    val endDay = java.time.temporal.ChronoUnit.DAYS.between(cycleStartDate, pmsEnd).toInt() + 1

    if (startDay > 0 && endDay <= cycleLength) {
        val startAngle = -90f + anglePerDay * (startDay - 1)
        val sweepAngle = anglePerDay * (endDay - startDay + 1)

        // Draw PMS window highlight
        drawArc(
            color = PMSPurple.copy(alpha = 0.2f),
            startAngle = startAngle,
            sweepAngle = sweepAngle,
            useCenter = false,
            topLeft = Offset(center.x - radius - 10.dp.toPx(), center.y - radius - 10.dp.toPx()),
            size = androidx.compose.ui.geometry.Size((radius + 10.dp.toPx()) * 2, (radius + 10.dp.toPx()) * 2),
            style = Stroke(width = 10.dp.toPx())
        )
    }
}

private fun DrawScope.drawPeriodDays(
    center: Offset,
    radius: Float,
    cycleStartDate: LocalDate,
    periodDays: List<LocalDate>,
    cycleLength: Int
) {
    val anglePerDay = 360f / cycleLength

    periodDays.forEach { periodDate ->
        val dayInCycle = java.time.temporal.ChronoUnit.DAYS.between(cycleStartDate, periodDate).toInt() + 1

        if (dayInCycle > 0 && dayInCycle <= cycleLength) {
            val angle = -90f + anglePerDay * (dayInCycle - 1)
            val angleRad = Math.toRadians(angle.toDouble())

            val indicatorX = center.x + radius * cos(angleRad).toFloat()
            val indicatorY = center.y + radius * sin(angleRad).toFloat()

            // Draw period day indicator
            drawCircle(
                color = PeriodRed,
                radius = 6.dp.toPx(),
                center = Offset(indicatorX, indicatorY)
            )
        }
    }
}

private fun DrawScope.drawNextPeriodPrediction(
    center: Offset,
    radius: Float,
    cycleStartDate: LocalDate,
    predictionDate: LocalDate,
    cycleLength: Int
) {
    val dayInCycle = java.time.temporal.ChronoUnit.DAYS.between(cycleStartDate, predictionDate).toInt() + 1

    // If prediction is beyond current cycle, show it at the start position
    val displayDay = if (dayInCycle > cycleLength) 1 else dayInCycle

    if (displayDay > 0) {
        val anglePerDay = 360f / cycleLength
        val angle = -90f + anglePerDay * (displayDay - 1)
        val angleRad = Math.toRadians(angle.toDouble())

        val indicatorX = center.x + (radius + 25.dp.toPx()) * cos(angleRad).toFloat()
        val indicatorY = center.y + (radius + 25.dp.toPx()) * sin(angleRad).toFloat()

        // Draw prediction indicator with pulsing effect
        drawCircle(
            color = PeriodRed.copy(alpha = 0.3f),
            radius = 15.dp.toPx(),
            center = Offset(indicatorX, indicatorY)
        )

        drawCircle(
            color = PeriodRed,
            radius = 8.dp.toPx(),
            center = Offset(indicatorX, indicatorY)
        )

        // Draw "P" for prediction
        drawCircle(
            color = White,
            radius = 6.dp.toPx(),
            center = Offset(indicatorX, indicatorY)
        )
    }
}

@Composable
private fun CenterContent(
    cycleDay: Int,
    phase: CyclePhase,
    currentDate: LocalDate
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Day",
            style = MaterialTheme.typography.labelMedium,
            color = DarkGray.copy(alpha = 0.7f)
        )
        Text(
            text = cycleDay.toString(),
            style = MaterialTheme.typography.displayMedium,
            color = PeriodPalThemeColors.getPhaseColor(phase),
            fontWeight = FontWeight.Bold
        )
        Text(
            text = phase.displayName,
            style = MaterialTheme.typography.titleSmall,
            color = DarkGray,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun PhaseLegend(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        LegendItem("Period", PeriodRed)
        LegendItem("Follicular", SoftPink40)
        LegendItem("Ovulation", FertileGreen)
        LegendItem("Luteal", PMSPurple)
    }
}

@Composable
private fun LegendItem(
    label: String,
    color: Color
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(color)
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray
        )
    }
}

@Preview(showBackground = true, name = "Circular Calendar Widget Preview")
@Composable
fun CircularCalendarWidgetPreview() {
    val today = LocalDate.now()
    val cycleStart = today.minusDays(14)

    PeriodPalTheme {
        CircularCalendarWidget(
            currentDate = today,
            cycleDay = 15,
            phase = CyclePhase.OVULATION,
            onDateSelected = { },
            cycleStartDate = cycleStart,
            fertileWindow = Pair(today.minusDays(2), today.plusDays(2)),
            pmsWindow = Pair(today.plusDays(10), today.plusDays(16)),
            periodDays = listOf(
                cycleStart,
                cycleStart.plusDays(1),
                cycleStart.plusDays(2),
                cycleStart.plusDays(3),
                cycleStart.plusDays(4)
            ),
            nextPeriodPrediction = today.plusDays(14)
        )
    }
}
