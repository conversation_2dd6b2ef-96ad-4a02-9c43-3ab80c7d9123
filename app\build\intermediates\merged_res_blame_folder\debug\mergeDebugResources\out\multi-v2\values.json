{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-45:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fce910b49377672473080ebeba046646\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "216,217", "startColumns": "4,4", "startOffsets": "13534,13590", "endColumns": "55,54", "endOffsets": "13585,13640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3684f126870460cbf44e1e8c4b7b80a6\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "127", "startColumns": "4", "startOffsets": "7358", "endColumns": "82", "endOffsets": "7436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09a3a233c8f6de7d440dffb5c239312f\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "99,102", "startColumns": "4,4", "startOffsets": "5863,5987", "endColumns": "53,66", "endOffsets": "5912,6049"}}, {"source": "D:\\PeriodPal\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "46", "endOffsets": "58"}, "to": {"startLines": "128", "startColumns": "4", "startOffsets": "7441", "endColumns": "46", "endOffsets": "7483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b176e8aba06099230d4846cfebebfa3\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "125,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160,161,162,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,221,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7215,8455,8543,8629,8710,8794,8863,8928,9011,9117,9203,9323,9377,9446,9507,9576,9665,9760,9834,9931,10024,10122,10271,10362,10450,10546,10644,10708,10776,10863,10957,11024,11096,11168,11269,11378,11454,11523,11571,11637,11701,11775,11832,11889,11961,12011,12065,12136,12207,12277,12346,12404,12480,12551,12625,12711,12761,12831,13757,14472", "endLines": "125,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159,160,161,162,163,164,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,230,233", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7283,8538,8624,8705,8789,8858,8923,9006,9112,9198,9318,9372,9441,9502,9571,9660,9755,9829,9926,10019,10117,10266,10357,10445,10541,10639,10703,10771,10858,10952,11019,11091,11163,11264,11373,11449,11518,11566,11632,11696,11770,11827,11884,11956,12006,12060,12131,12202,12272,12341,12399,12475,12546,12620,12706,12756,12826,12891,14467,14620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffaf60804bb777caaab07cd66703845d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,97,98,124,136,137,138,139,140,141,142,204,205,206,207,208,209,210,211,213,214,215,218,234,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3594,3653,3712,3772,3832,3892,3952,4012,4072,4132,4192,4252,4312,4371,4431,4491,4551,4611,4671,4731,4791,4851,4911,4971,5030,5090,5150,5209,5268,5327,5386,5445,5504,5578,5636,5757,5808,7162,7999,8064,8118,8184,8285,8343,8395,12896,12958,13012,13062,13116,13162,13208,13250,13361,13408,13444,13645,14625,14736", "endLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,97,98,124,136,137,138,139,140,141,142,204,205,206,207,208,209,210,211,213,214,215,220,236,240", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "3648,3707,3767,3827,3887,3947,4007,4067,4127,4187,4247,4307,4366,4426,4486,4546,4606,4666,4726,4786,4846,4906,4966,5025,5085,5145,5204,5263,5322,5381,5440,5499,5573,5631,5686,5803,5858,7210,8059,8113,8179,8280,8338,8390,8450,12953,13007,13057,13111,13157,13203,13245,13285,13403,13439,13529,13752,14731,14926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd1fb44d3025048634a00a9f3a07ea5c\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "5691", "endColumns": "65", "endOffsets": "5752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b53b0a6e7beae069ef1b2e945452fd0d\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "6955", "endColumns": "42", "endOffsets": "6993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eb80c39fd83dc09184ca43a2c381fdcb\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7058", "endColumns": "53", "endOffsets": "7107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b77f7f969be2bb725302f33ce1d15eb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "103,121", "startColumns": "4,4", "startOffsets": "6054,6998", "endColumns": "41,59", "endOffsets": "6091,7053"}}, {"source": "D:\\PeriodPal\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "20,19,31,23,32,17,18,16,26,25,24,35,36,37,6,5,4,3,12,11,10,9,29,38,39,30,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,738,1205,881,1251,643,690,597,1025,978,931,1343,1390,1437,240,191,142,93,477,428,379,330,1104,1484,1529,1154,555", "endColumns": "41,45,45,49,45,46,47,45,50,46,46,46,46,46,48,48,48,48,48,48,48,48,49,44,44,50,41", "endOffsets": "821,779,1246,926,1292,685,733,638,1071,1020,973,1385,1432,1479,284,235,186,137,521,472,423,374,1149,1524,1569,1200,592"}, "to": {"startLines": "5,8,9,10,11,12,13,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,543,589,635,685,731,778,958,1004,1055,1102,1149,1196,1243,1290,1339,1388,1437,1486,1535,1584,1633,1682,1732,1777,1822,1873", "endColumns": "41,45,45,49,45,46,47,45,50,46,46,46,46,46,48,48,48,48,48,48,48,48,49,44,44,50,41", "endOffsets": "407,584,630,680,726,773,821,999,1050,1097,1144,1191,1238,1285,1334,1383,1432,1481,1530,1579,1628,1677,1727,1772,1817,1868,1910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fe94774b27fdaa35093098e8a904795c\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7112", "endColumns": "49", "endOffsets": "7157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee81e9003baef06be7850ff4f00325b8\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,14,15,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,100,101,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,126,129,130,131,132,133,134,135,212,241,242,246,247,251,253,254,255,261,271,304,325,358", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,826,895,1915,1985,2053,2125,2195,2256,2330,2403,2464,2525,2587,2651,2713,2774,2842,2942,3002,3068,3141,3210,3267,3319,3381,3453,3529,5917,5952,6096,6151,6214,6269,6327,6385,6446,6509,6566,6617,6667,6728,6785,6851,6885,6920,7288,7488,7555,7627,7696,7765,7839,7911,13290,14931,15048,15249,15359,15560,15775,15847,15914,16117,16418,18149,18830,19512", "endLines": "2,3,4,6,7,14,15,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,100,101,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,126,129,130,131,132,133,134,135,212,241,245,246,250,251,253,254,260,270,303,324,357,363", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,890,953,1980,2048,2120,2190,2251,2325,2398,2459,2520,2582,2646,2708,2769,2837,2937,2997,3063,3136,3205,3262,3314,3376,3448,3524,3589,5947,5982,6146,6209,6264,6322,6380,6441,6504,6561,6612,6662,6723,6780,6846,6880,6915,6950,7353,7550,7622,7691,7760,7834,7906,7994,13356,15043,15244,15354,15555,15684,15842,15909,16112,16413,18144,18825,19507,19674"}}, {"source": "D:\\PeriodPal\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "86", "endOffsets": "138"}, "to": {"startLines": "252", "startColumns": "4", "startOffsets": "15689", "endColumns": "85", "endOffsets": "15770"}}]}]}