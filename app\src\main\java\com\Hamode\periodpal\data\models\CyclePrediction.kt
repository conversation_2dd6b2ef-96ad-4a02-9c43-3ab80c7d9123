package com.Hamode.periodpal.data.models

import java.time.LocalDate

/**
 * Data class representing cycle predictions and forecasts
 */
data class CyclePrediction(
    val nextPeriodStart: LocalDate,
    val nextPeriodEnd: LocalDate,
    val nextFertileWindowStart: LocalDate,
    val nextFertileWindowEnd: LocalDate,
    val nextPMSStart: LocalDate,
    val confidence: Double, // 0.0 to 1.0
    val basedOnCycles: Int
) {
    /**
     * Gets the predicted period duration in days
     */
    fun getPredictedPeriodDuration(): Int {
        return java.time.temporal.ChronoUnit.DAYS.between(nextPeriodStart, nextPeriodEnd).toInt() + 1
    }
    
    /**
     * Gets the predicted fertile window duration in days
     */
    fun getFertileWindowDuration(): Int {
        return java.time.temporal.ChronoUnit.DAYS.between(nextFertileWindowStart, nextFertileWindowEnd).toInt() + 1
    }
    
    /**
     * Gets the predicted PMS duration in days
     */
    fun getPMSDuration(): Int {
        return java.time.temporal.ChronoUnit.DAYS.between(nextPMSStart, nextPeriodStart).toInt()
    }
    
    /**
     * Checks if the prediction is reliable based on confidence and data
     */
    fun isReliable(): Boolean {
        return confidence >= 0.7 && basedOnCycles >= 3
    }
    
    /**
     * Gets confidence level description
     */
    fun getConfidenceDescription(): String {
        return when {
            confidence >= 0.9 -> "Very High"
            confidence >= 0.8 -> "High"
            confidence >= 0.7 -> "Good"
            confidence >= 0.6 -> "Moderate"
            confidence >= 0.5 -> "Low"
            else -> "Very Low"
        }
    }
    
    /**
     * Checks if currently in fertile window
     */
    fun isCurrentlyFertile(): Boolean {
        val today = LocalDate.now()
        return !today.isBefore(nextFertileWindowStart) && !today.isAfter(nextFertileWindowEnd)
    }
    
    /**
     * Checks if currently in PMS period
     */
    fun isCurrentlyPMS(): Boolean {
        val today = LocalDate.now()
        return !today.isBefore(nextPMSStart) && today.isBefore(nextPeriodStart)
    }
    
    /**
     * Gets days until next period
     */
    fun getDaysUntilNextPeriod(): Int {
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), nextPeriodStart).toInt()
    }
    
    /**
     * Gets days until fertile window
     */
    fun getDaysUntilFertileWindow(): Int {
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), nextFertileWindowStart).toInt()
    }
}
