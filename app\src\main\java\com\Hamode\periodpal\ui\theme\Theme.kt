package com.Hamode.periodpal.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    primary = RosePink60,
    secondary = SoftPink60,
    tertiary = RosePink80,
    background = DarkGray,
    surface = Color(0xFF2C2C2C),
    onPrimary = White,
    onSecondary = White,
    onTertiary = DarkGray,
    onBackground = White,
    onSurface = White,
    primaryContainer = RosePink20,
    onPrimaryContainer = RosePink80,
    secondaryContainer = SoftPink20,
    onSecondaryContainer = SoftPink80,
    error = ErrorRed,
    onError = White,
    errorContainer = Color(0xFF93000A),
    onErrorContainer = Color(0xFFFFDAD6)
)

private val LightColorScheme = lightColorScheme(
    primary = RosePink40,
    secondary = SoftPink40,
    tertiary = RosePink60,
    background = OffWhite,
    surface = White,
    onPrimary = White,
    onSecondary = White,
    onTertiary = White,
    onBackground = DarkGray,
    onSurface = DarkGray,
    primaryContainer = SoftPink80,
    onPrimaryContainer = RosePink20,
    secondaryContainer = SoftPink80,
    onSecondaryContainer = RosePink20,
    surfaceVariant = LightGray,
    onSurfaceVariant = DarkGray,
    outline = MediumGray,
    outlineVariant = LightGray,
    error = ErrorRed,
    onError = White,
    errorContainer = Color(0xFFFFDAD6),
    onErrorContainer = Color(0xFF410002),
    inverseSurface = DarkGray,
    inverseOnSurface = OffWhite,
    inversePrimary = RosePink80
)

@Composable
fun PeriodPalTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}