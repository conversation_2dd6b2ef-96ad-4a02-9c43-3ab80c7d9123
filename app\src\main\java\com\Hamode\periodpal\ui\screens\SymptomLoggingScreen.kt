package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.components.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SymptomLoggingScreen(
    selectedDate: LocalDate,
    initialDailyLog: DailyLog? = null,
    onSave: (DailyLog) -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var flowIntensity by remember { mutableStateOf(initialDailyLog?.flowIntensity ?: FlowIntensity.NONE) }
    var selectedMoods by remember { mutableStateOf(initialDailyLog?.moods?.toSet() ?: emptySet()) }
    var painLevel by remember { mutableStateOf(initialDailyLog?.painLevel ?: PainLevel.NONE) }
    var selectedSymptoms by remember { mutableStateOf(initialDailyLog?.symptoms?.toSet() ?: emptySet()) }
    var notes by remember { mutableStateOf(initialDailyLog?.notes ?: "") }
    var weight by remember { mutableStateOf(initialDailyLog?.weight?.toString() ?: "") }
    var temperature by remember { mutableStateOf(initialDailyLog?.temperature?.toString() ?: "") }
    var sleepHours by remember { mutableStateOf(initialDailyLog?.sleepHours?.toString() ?: "") }
    var waterIntake by remember { mutableStateOf(initialDailyLog?.waterIntake?.toString() ?: "") }
    var exerciseMinutes by remember { mutableStateOf(initialDailyLog?.exerciseMinutes?.toString() ?: "") }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        SoftPink80.copy(alpha = 0.1f)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Column {
                    Text(
                        text = "Daily Log",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = selectedDate.format(DateTimeFormatter.ofPattern("EEEE, MMM dd, yyyy")),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                Button(
                    onClick = {
                        val dailyLog = DailyLog(
                            date = selectedDate,
                            flowIntensity = flowIntensity,
                            moods = selectedMoods.toList(),
                            painLevel = painLevel,
                            symptoms = selectedSymptoms.toList(),
                            notes = notes,
                            weight = weight.toDoubleOrNull(),
                            temperature = temperature.toDoubleOrNull(),
                            sleepHours = sleepHours.toDoubleOrNull(),
                            waterIntake = waterIntake.toIntOrNull(),
                            exerciseMinutes = exerciseMinutes.toIntOrNull()
                        )
                        onSave(dailyLog)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = RosePink40
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Save",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Save")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )
        
        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Flow Intensity Section
            EnhancedFlowIntensitySelector(
                selectedIntensity = flowIntensity,
                onIntensitySelected = { flowIntensity = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Mood Section
            EnhancedMoodSelector(
                selectedMoods = selectedMoods,
                onMoodsChanged = { selectedMoods = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Pain Level Section
            EnhancedPainLevelSelector(
                selectedPainLevel = painLevel,
                onPainLevelChanged = { painLevel = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Symptoms Section
            CategorizedSymptomsSelector(
                selectedSymptoms = selectedSymptoms,
                onSymptomsChanged = { selectedSymptoms = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Additional Metrics
            AdditionalMetricsSection(
                weight = weight,
                onWeightChanged = { weight = it },
                temperature = temperature,
                onTemperatureChanged = { temperature = it },
                sleepHours = sleepHours,
                onSleepHoursChanged = { sleepHours = it },
                waterIntake = waterIntake,
                onWaterIntakeChanged = { waterIntake = it },
                exerciseMinutes = exerciseMinutes,
                onExerciseMinutesChanged = { exerciseMinutes = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Notes Section
            NotesSection(
                notes = notes,
                onNotesChanged = { notes = it },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Bottom spacing
            Spacer(modifier = Modifier.height(20.dp))
        }
    }
}

@Preview(showBackground = true, name = "Symptom Logging Screen Preview")
@Composable
fun SymptomLoggingScreenPreview() {
    PeriodPalTheme {
        SymptomLoggingScreen(
            selectedDate = LocalDate.now(),
            onSave = { },
            onBack = { }
        )
    }
}
