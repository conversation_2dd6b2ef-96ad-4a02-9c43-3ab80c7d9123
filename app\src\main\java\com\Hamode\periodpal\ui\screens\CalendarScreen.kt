package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Today
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.components.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    onNavigateToSymptomLogging: (LocalDate) -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    var currentCycleDay by remember { mutableStateOf(15) }
    var currentPhase by remember { mutableStateOf(CyclePhase.FOLLICULAR) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        SoftPink80.copy(alpha = 0.1f)
                    )
                )
            )
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Calendar",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold
                )
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = { selectedDate = LocalDate.now() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Today,
                        contentDescription = "Go to today"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )
        
        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Calendar Widget
            CalendarViewPager(
                currentDate = selectedDate,
                cycleDay = currentCycleDay,
                phase = currentPhase,
                onDateSelected = { date ->
                    selectedDate = date
                    // Update cycle day based on selected date
                    // This would normally come from your data layer
                },
                cycleStartDate = selectedDate.minusDays(currentCycleDay.toLong() - 1),
                fertileWindow = Pair(
                    selectedDate.minusDays(2),
                    selectedDate.plusDays(2)
                ),
                pmsWindow = Pair(
                    selectedDate.plusDays(10),
                    selectedDate.plusDays(16)
                ),
                periodDays = listOf(
                    selectedDate.minusDays(currentCycleDay.toLong() - 1),
                    selectedDate.minusDays(currentCycleDay.toLong() - 2),
                    selectedDate.minusDays(currentCycleDay.toLong() - 3)
                ),
                nextPeriodPrediction = selectedDate.plusDays(14),
                calendarData = getSampleCalendarData(selectedDate),
                modifier = Modifier.fillMaxWidth()
            )
            
            // Selected Date Info
            SelectedDateCard(
                selectedDate = selectedDate,
                cycleDay = currentCycleDay,
                phase = currentPhase,
                onLogSymptoms = { onNavigateToSymptomLogging(selectedDate) },
                modifier = Modifier.fillMaxWidth()
            )
            
            // Cycle Overview
            CycleOverviewCard(
                cycleStatistics = getSampleCycleStatistics(),
                modifier = Modifier.fillMaxWidth()
            )
            
            // Bottom spacing for navigation
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun SelectedDateCard(
    selectedDate: LocalDate,
    cycleDay: Int,
    phase: CyclePhase,
    onLogSymptoms: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Selected Date",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    Text(
                        text = selectedDate.format(java.time.format.DateTimeFormatter.ofPattern("EEEE, MMM dd, yyyy")),
                        style = MaterialTheme.typography.bodyMedium,
                        color = RosePink40
                    )
                }
                
                Button(
                    onClick = onLogSymptoms,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = RosePink40
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "Log Symptoms",
                        color = White
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Cycle info for selected date
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                CycleInfoItem(
                    label = "Cycle Day",
                    value = cycleDay.toString(),
                    color = RosePink40
                )
                
                CycleInfoItem(
                    label = "Phase",
                    value = phase.displayName,
                    color = PeriodPalThemeColors.getPhaseColor(phase)
                )
                
                CycleInfoItem(
                    label = "Status",
                    value = if (selectedDate == LocalDate.now()) "Today" else 
                            if (selectedDate.isBefore(LocalDate.now())) "Past" else "Future",
                    color = if (selectedDate == LocalDate.now()) SuccessGreen else DarkGray.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
private fun CycleInfoItem(
    label: String,
    value: String,
    color: androidx.compose.ui.graphics.Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall,
            color = color,
            fontWeight = FontWeight.SemiBold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = DarkGray.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun getSampleCalendarData(selectedDate: LocalDate): Map<LocalDate, CalendarDayData> {
    return mapOf(
        selectedDate to CalendarDayData(
            date = selectedDate,
            isToday = selectedDate == LocalDate.now(),
            cycleDay = 15,
            phase = CyclePhase.OVULATION,
            isFertile = true
        ),
        selectedDate.minusDays(10) to CalendarDayData(
            date = selectedDate.minusDays(10),
            flowIntensity = FlowIntensity.MEDIUM,
            cycleDay = 5
        ),
        selectedDate.plusDays(5) to CalendarDayData(
            date = selectedDate.plusDays(5),
            isPMS = true,
            hasSymptoms = true
        )
    )
}

@Composable
private fun getSampleCycleStatistics(): CycleStatistics {
    return CycleStatistics(
        averageCycleLength = 28.5,
        averagePeriodLength = 5.2,
        cycleVariability = 2.1,
        totalCycles = 12,
        longestCycle = 32,
        shortestCycle = 26,
        mostCommonSymptoms = listOf(
            Pair(SymptomType.CRAMPS, 8),
            Pair(SymptomType.BLOATING, 6)
        ),
        averagePainLevel = 2.3,
        moodTrends = mapOf(
            MoodType.HAPPY to 15,
            MoodType.TIRED to 12
        )
    )
}

@Preview(showBackground = true, name = "Calendar Screen Preview")
@Composable
fun CalendarScreenPreview() {
    PeriodPalTheme {
        CalendarScreen(
            onNavigateToSymptomLogging = { },
            onNavigateBack = { }
        )
    }
}
