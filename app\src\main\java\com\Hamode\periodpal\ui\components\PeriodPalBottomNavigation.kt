package com.Hamode.periodpal.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.semantics.*
import androidx.compose.foundation.border
import com.Hamode.periodpal.ui.accessibility.*
import com.Hamode.periodpal.ui.navigation.NavigationDestination
import com.Hamode.periodpal.ui.theme.*

@Composable
fun PeriodPalBottomNavigation(
    currentDestination: NavigationDestination,
    onNavigate: (NavigationDestination) -> Unit,
    notificationCounts: Map<NavigationDestination, Int> = emptyMap(),
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.surface,
                            SoftPink80.copy(alpha = 0.1f),
                            MaterialTheme.colorScheme.surface
                        )
                    )
                )
                .padding(vertical = 12.dp, horizontal = 8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                NavigationDestination.getMainNavigationItems().forEach { destination ->
                    BottomNavigationItem(
                        destination = destination,
                        isSelected = currentDestination == destination,
                        onClick = { onNavigate(destination) },
                        notificationCount = notificationCounts[destination] ?: 0,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun BottomNavigationItem(
    destination: NavigationDestination,
    isSelected: Boolean,
    onClick: () -> Unit,
    notificationCount: Int,
    modifier: Modifier = Modifier
) {
    val accessibilitySettings = rememberAccessibilitySettings()
    var isFocused by remember { mutableStateOf(false) }
    val animatedScale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = tween(
            durationMillis = AccessibilityUtils.getAnimationDuration(200, accessibilitySettings)
        ),
        label = "scale"
    )
    
    val animatedIconColor by animateColorAsState(
        targetValue = AccessibilityUtils.getAccessibleColor(
            if (isSelected) RosePink40 else DarkGray.copy(alpha = 0.6f),
            settings = accessibilitySettings
        ),
        animationSpec = tween(
            durationMillis = AccessibilityUtils.getAnimationDuration(200, accessibilitySettings)
        ),
        label = "iconColor"
    )

    val animatedTextColor by animateColorAsState(
        targetValue = AccessibilityUtils.getAccessibleColor(
            if (isSelected) RosePink40 else DarkGray.copy(alpha = 0.7f),
            settings = accessibilitySettings
        ),
        animationSpec = tween(
            durationMillis = AccessibilityUtils.getAnimationDuration(200, accessibilitySettings)
        ),
        label = "textColor"
    )

    val animatedBackgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            AccessibilityUtils.getAccessibleColor(
                RosePink40.copy(alpha = 0.1f),
                settings = accessibilitySettings
            )
        } else Color.Transparent,
        animationSpec = tween(
            durationMillis = AccessibilityUtils.getAnimationDuration(200, accessibilitySettings)
        ),
        label = "backgroundColor"
    )

    // Focus indicator color
    val focusIndicatorColor = if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) {
        AccessibilityColors.getFocusIndicatorColor()
    } else Color.Transparent
    
    Column(
        modifier = modifier
            .defaultMinSize(
                minWidth = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings),
                minHeight = AccessibilityUtils.getMinimumTouchTargetSize(accessibilitySettings)
            )
            .clip(RoundedCornerShape(16.dp))
            .background(animatedBackgroundColor)
            .border(
                width = if (isFocused && accessibilitySettings.isFocusIndicatorEnabled) 2.dp else 0.dp,
                color = focusIndicatorColor,
                shape = RoundedCornerShape(16.dp)
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onClick() }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .padding(vertical = 8.dp, horizontal = 4.dp)
            .scale(animatedScale)
            .semantics {
                contentDescription = SemanticDescriptions.getNavigationDescription(
                    destination.title,
                    isSelected
                )
                role = Role.Tab
                selected = isSelected
                if (notificationCount > 0) {
                    stateDescription = "$notificationCount notifications"
                }
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box {
            Icon(
                imageVector = if (isSelected) destination.selectedIcon else destination.unselectedIcon,
                contentDescription = destination.title,
                tint = animatedIconColor,
                modifier = Modifier.size(24.dp)
            )
            
            // Notification badge
            if (destination.hasNotificationBadge && notificationCount > 0) {
                NotificationBadge(
                    count = notificationCount,
                    modifier = Modifier.align(Alignment.TopEnd)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = destination.title,
            style = AccessibilityUtils.getAccessibleTextStyle(
                MaterialTheme.typography.labelSmall,
                accessibilitySettings
            ),
            color = animatedTextColor,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

@Composable
private fun NotificationBadge(
    count: Int,
    modifier: Modifier = Modifier
) {
    if (count > 0) {
        Box(
            modifier = modifier
                .size(16.dp)
                .background(
                    color = ErrorRed,
                    shape = CircleShape
                )
                .offset(x = 4.dp, y = (-4).dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (count > 99) "99+" else count.toString(),
                style = MaterialTheme.typography.labelSmall.copy(fontSize = 10.sp),
                color = White,
                fontWeight = FontWeight.Bold,
                maxLines = 1
            )
        }
    }
}

@Composable
fun FloatingActionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isVisible: Boolean = true
) {
    if (isVisible) {
        androidx.compose.material3.FloatingActionButton(
            onClick = onClick,
            modifier = modifier,
            containerColor = RosePink40,
            contentColor = White,
            elevation = FloatingActionButtonDefaults.elevation(
                defaultElevation = 6.dp,
                pressedElevation = 8.dp
            )
        ) {
            Icon(
                imageVector = androidx.compose.material.icons.Icons.Default.Add,
                contentDescription = "Quick log",
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

@Preview(showBackground = true, name = "Bottom Navigation Preview")
@Composable
fun PeriodPalBottomNavigationPreview() {
    PeriodPalTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.Bottom
        ) {
            PeriodPalBottomNavigation(
                currentDestination = NavigationDestination.HOME,
                onNavigate = { },
                notificationCounts = mapOf(
                    NavigationDestination.HEALTH_LOGS to 3
                )
            )
        }
    }
}

@Preview(showBackground = true, name = "Bottom Navigation - Calendar Selected")
@Composable
fun PeriodPalBottomNavigationCalendarPreview() {
    PeriodPalTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.Bottom
        ) {
            PeriodPalBottomNavigation(
                currentDestination = NavigationDestination.CALENDAR,
                onNavigate = { },
                notificationCounts = mapOf(
                    NavigationDestination.HEALTH_LOGS to 5,
                    NavigationDestination.INSIGHTS to 1
                )
            )
        }
    }
}
