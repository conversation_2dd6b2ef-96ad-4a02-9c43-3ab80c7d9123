package com.Hamode.periodpal.data.models

/**
 * Represents different mood states that can be tracked
 */
enum class MoodType(val displayName: String, val emoji: String) {
    HAPPY("Happy", "😊"),
    SAD("Sad", "😢"),
    ANXIOUS("Anxious", "😰"),
    IRRITABLE("Irritable", "😤"),
    CALM("Calm", "😌"),
    ENERGETIC("Energetic", "⚡"),
    TIRED("Tired", "😴"),
    EMOTIONAL("Emotional", "🥺"),
    CONFIDENT("Confident", "💪"),
    STRESSED("Stressed", "😫");
    
    companion object {
        fun getPositiveMoods(): List<MoodType> {
            return listOf(HAPPY, CALM, ENERGETIC, CONFIDENT)
        }
        
        fun getNegativeMoods(): List<MoodType> {
            return listOf(SAD, ANXIOUS, IRRITABLE, TIRED, EMOTIONAL, STRESSED)
        }
    }
}
