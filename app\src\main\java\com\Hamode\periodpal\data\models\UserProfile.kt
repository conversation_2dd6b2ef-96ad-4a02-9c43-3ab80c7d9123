package com.Hamode.periodpal.data.models

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Represents user profile and preferences
 */
data class UserProfile(
    val id: String = "",
    val name: String = "",
    val dateOfBirth: LocalDate? = null,
    val averageCycleLength: Int = 28,
    val averagePeriodLength: Int = 5,
    val lastPeriodStartDate: LocalDate? = null,
    val isPregnancyMode: Boolean = false,
    val isMenopauseMode: Boolean = false,
    val trackingPreferences: TrackingPreferences = TrackingPreferences(),
    val notificationSettings: NotificationSettings = NotificationSettings(),
    val emergencyContacts: List<EmergencyContact> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    /**
     * Gets predicted next period start date
     */
    fun getNextPeriodPrediction(): LocalDate? {
        return lastPeriodStartDate?.plusDays(averageCycleLength.toLong())
    }
    
    /**
     * Gets predicted fertile window for current cycle
     */
    fun getCurrentFertileWindow(): Pair<LocalDate, LocalDate>? {
        val nextPeriod = getNextPeriodPrediction() ?: return null
        val ovulationDate = nextPeriod.minusDays((averageCycleLength / 2).toLong())
        return Pair(
            ovulationDate.minusDays(2),
            ovulationDate.plusDays(2)
        )
    }
    
    /**
     * Gets current cycle day
     */
    fun getCurrentCycleDay(): Int? {
        val lastPeriod = lastPeriodStartDate ?: return null
        val today = LocalDate.now()
        return java.time.temporal.ChronoUnit.DAYS.between(lastPeriod, today).toInt() + 1
    }
}

/**
 * User's tracking preferences
 */
data class TrackingPreferences(
    val trackMood: Boolean = true,
    val trackSymptoms: Boolean = true,
    val trackPain: Boolean = true,
    val trackWeight: Boolean = false,
    val trackTemperature: Boolean = false,
    val trackSleep: Boolean = false,
    val trackWater: Boolean = false,
    val trackExercise: Boolean = false,
    val trackMedication: Boolean = false,
    val reminderTime: String = "09:00", // HH:mm format
    val enablePeriodReminders: Boolean = true,
    val enableFertilityReminders: Boolean = true,
    val enablePMSReminders: Boolean = true
)

/**
 * Notification settings
 */
data class NotificationSettings(
    val periodReminder: Boolean = true,
    val periodReminderDaysBefore: Int = 2,
    val fertileWindowReminder: Boolean = true,
    val pmsReminder: Boolean = true,
    val pmsReminderDaysBefore: Int = 3,
    val dailyLogReminder: Boolean = true,
    val dailyLogReminderTime: String = "21:00", // HH:mm format
    val medicationReminders: Boolean = false,
    val appointmentReminders: Boolean = true
)

/**
 * Emergency contact information
 */
data class EmergencyContact(
    val id: String = "",
    val name: String,
    val relationship: String, // e.g., "Doctor", "Partner", "Mother"
    val phoneNumber: String,
    val email: String = "",
    val isDoctor: Boolean = false,
    val specialization: String = "" // e.g., "Gynecologist"
)
